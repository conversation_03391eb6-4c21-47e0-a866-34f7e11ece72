#ifndef DECODED_INSTRUCTION_H
#define DECODED_INSTRUCTION_H

#include "register.h"
#include <Zydis/Zydis.h>
#include <array>
#include <cstdint>
#include <string>

namespace x86_64 {

// Forward declaration
enum class InstructionType;

enum class ConditionCode {
  O,     // Overflow
  NO,    // No Overflow
  B,     // Below
  NB,    // Not Below
  Z,     // Zero
  NZ,    // Not Zero
  BE,    // Below or Equal
  NBE,   // Not Below or Equal
  S,     // Sign
  NS,    // Not Sign
  P,     // Parity
  NP,    // Not Parity
  L,     // Less
  NL,    // Not Less
  LE,    // Less or Equal
  NLE,   // Not Less or Equal
  CXZ,   // CX is Zero
  ECXZ,  // ECX is Zero
  RCXZ,  // RCX is Zero
  Always // Default case
};

// Operator overloads for ConditionCode
inline uint8_t operator+(uint8_t lhs, ConditionCode rhs) {
  return lhs + static_cast<uint8_t>(rhs);
}

inline uint8_t operator+(ConditionCode lhs, uint8_t rhs) {
  return static_cast<uint8_t>(lhs) + rhs;
}

// Comprehensive InstructionType enum
enum class InstructionType {
  // Basic instructions
  Unknown,
  Nop,
  Mov,
  Add,
  Sub,
  Mul,
  Imul,
  Div,
  Idiv,
  And,
  Or,
  Xor,
  Not,
  Neg,
  Cmp,
  Test,
  Inc,
  Dec,
  Shl,
  Sal, // Arithmetic shift left (same as SHL)
  Shr,
  Sar,
  Rol,
  Ror,
  Adc,
  Sbb,
  Xchg,
  Movsx,
  Movzx,
  Lea,

  // Control flow
  Jmp,
  Jump, // Alternative name for Jmp
  Jcc,
  Call,
  Call_far,
  Ret,
  Ret_far,
  Push,
  Pop,
  Pusha,
  Popa,
  Pushf,
  Pushfd,
  Pushfq,
  Popf,
  Popfd,
  Popfq,
  Loop,
  Loope,
  Loopne,
  Jecxz,
  Jrcxz,

  // System instructions
  Syscall,
  Sysret,
  Int,
  Iret,
  Iretd,
  Iretq,
  Hlt,
  Cli,
  Sti,
  Cld,
  Std,
  Cmovcc,
  Setcc,
  Cpuid,
  Rdtsc,
  Invd,
  Wbinvd,
  Xgetbv,
  Xsetbv,
  Lgdt,
  Lidt,
  Ltr,
  Lmsw,
  Mov_CR,
  Mov_DR,
  Rdmsr,
  Wrmsr,

  // String operations
  Movsb,
  Movsw,
  Movsd_str, // String move double word
  Movsq,
  Stosb,
  Stosw,
  Stosd,
  Stosq,
  Lodsb,
  Lodsw,
  Lodsd,
  Lodsq,
  Scasb,
  Scasw,
  Scasd,
  Scasq,
  Cmpsb,
  Cmpsw,
  Cmpsd,
  Cmpsq,

  // FPU Instructions (x87)
  Fadd,
  Fsub,
  Fmul,
  Fdiv,
  Fsqrt,
  Fcom,
  Fld,
  Fst,
  Fstp, // Added missing Fstp instruction
  Fchs,
  Fabs,
  Fcos,
  Fsin,
  Ftan,
  Fpatan,
  F2xm1,
  Fyl2x,
  Fyl2xp1,
  Fscale,
  Fprem,
  Fprem1,
  Fxch,
  Fnop,
  Fclex,
  Finit,
  Fwait,

  // SSE/SSE2/SSE3/SSSE3/SSE4.1/SSE4.2 Instructions
  Movaps,
  Movups,
  Movapd,
  Movupd,
  Movss,
  Movsd_sse, // SSE move scalar double-precision
  Addps,
  Subps,
  Mulps,
  Divps,
  Addss,
  Subss,
  Mulss,
  Divss,
  Addsd,
  Subsd,
  Mulsd,
  Divsd,
  Sqrtps,
  Sqrtsd,
  Sqrtss,
  Maxps,
  Minps,
  Andps,
  Orps,
  Xorps,
  Andpd,
  Orpd,
  Xorpd,
  Paddq,
  Psubq,
  Pmulld,
  Paddd,
  Psubd,
  Paddw,
  Psubw,
  Paddb,
  Psubb,
  Pcmpeqb,
  Pcmpeqw,
  Pcmpeqd,
  Pcmpeqq,
  Pmaxsw,
  Pminsw,
  Pshufd,
  Unpcklps,
  Unpckhps,
  Shufps,
  Cvtsi2ss,
  Cvtss2si,
  Cvtsi2sd,
  Cvtsd2si,
  Cvttss2si,
  Cvttsd2si,
  Comiss,
  Comisd,
  Ucomiss,
  Ucomisd,
  Movdqa,
  Addpd,
  Subpd,
  Mulpd,
  Divpd,
  Pand,
  Por,
  Pxor,

  // AVX/AVX2/AVX512 Instructions
  Vmovaps,
  Vmovups,
  Vaddps,
  Vsubps,
  Vmulps,
  Vdivps,
  Vfmadd132ps,
  Vfmadd213ps,
  Vfmadd231ps,
  Vaddpd,
  Vaddsd,
  Vaddss,
  Vsubpd,
  Vsubsd,
  Vsubss,
  Vmulpd,
  Vmulsd,
  Vmulss,
  Vdivpd,
  Vdivsd,
  Vdivss,
  Vsqrtpd,
  Vsqrtps, // Added missing Vsqrtps instruction
  Vsqrtsd,
  Vsqrtss,
  Vgatherdps,
  Vperm2f128,
  Vbroadcastss,
  Vroundps,
  Vtestps,
  Vzeroupper,
  Vmovaps_512,
  Vmovups_512,
  Vaddps_512,
  Vsubps_512,
  Vmulps_512,
  Vdivps_512,
  Vsqrtps_512,
  Vpaddd_zmm,
  Vpmullq_zmm,
  Vgatherdpd,
  Vcompressps,
  Vexpandps,
  Vfpclassps,
  Vgetexpps,
  Vreduceps,
  Vscatterqps,
  Vpmovm2d,
  Vpmovd2m,

  // VMX instructions
  Vmxon,
  Vmclear,
  Vmptrld,
  Vmptrst,
  Vmread,
  Vmwrite,
  Vmlaunch,
  Vmresume,
  Vmxoff,
  Vmcall,
  Invept,
  Invvpid,

  // Additional system instructions
  Rsm,
  Clflush,
  Prefetch,
  In,
  Out,
  Ins,
  Outs,
  Invlpg,

  // Floating point extensions
  Fsincos,
  Fptan,
  Fxtract,

  // Memory operations (for pipeline classification)
  Mov_Mem_Reg,
  Mov_Reg_Mem
};

struct DecodedInstruction {
  struct Operand {
    enum class Type {
      NONE,
      REGISTER,
      MEMORY,
      IMMEDIATE,
      POINTER,
      // Extended types for SIMD registers
      XMM,
      YMM,
      ZMM,
      MMX,
      ST,
      FPU, // Added FPU operand type
      SEGMENT,
      CONTROL,
      DEBUG
    };

    Type type;
    Register reg;       // Register (using Register enum)
    uint64_t immediate; // For immediate values
    uint8_t size;       // Size in bits
    uint8_t
        instructionLength; // Length of instruction for RIP-relative addressing

    struct Memory {
      Register base;        // Base register
      Register index;       // Index register
      uint8_t scale;        // Scale factor
      int64_t displacement; // Displacement value
    } memory;

    Operand()
        : type(Type::NONE), reg(Register::NONE), immediate(0), size(0),
          instructionLength(0) {
      memory.base = Register::NONE;
      memory.index = Register::NONE;
      memory.scale = 1;
      memory.displacement = 0;
    }

    void reset() {
      type = Type::NONE;
      reg = Register::NONE;
      immediate = 0;
      size = 0;
      instructionLength = 0;
      memory.base = Register::NONE;
      memory.index = Register::NONE;
      memory.scale = 1;
      memory.displacement = 0;
    }
  };

  uint64_t length;                 // Length of the instruction in bytes
  uint64_t pc;                     // Program counter
  uint32_t opcode;                 // Opcode value
  InstructionType instType;        // Instruction type
  ConditionCode conditionCode;     // Condition code for conditional jumps
  uint8_t operandCount;            // Number of operands
  std::array<Operand, 4> operands; // Up to 4 operands
  bool
      isReadModifyWrite; // True if instruction reads, modifies, and writes back

  // Execution timing information
  uint32_t estimated_cycles; // Estimated execution cycles for this instruction

  // Prefix flags
  bool repPrefix;
  bool repePrefix;
  bool repnePrefix;
  bool lockPrefix;
  bool operandSizeOverride;
  bool addressSizeOverride;
  uint8_t rex; // REX prefix value
  bool isVex;  // VEX encoding
  bool isEvex; // EVEX encoding

  // EVEX encoding information
  struct EvexInfo {
    bool valid;
    uint8_t aaa() const {
      return mask_reg;
    } // Embedded opmask register specifier
    bool z() const { return zero_masking; } // Zeroing/Merging flag

    uint8_t mask_reg;
    bool zero_masking;
    uint8_t vector_length;
    uint8_t broadcast;

    EvexInfo()
        : valid(false), mask_reg(0), zero_masking(false), vector_length(0),
          broadcast(0) {}
  } evexInfo;

  DecodedInstruction()
      : length(0), pc(0), opcode(0), instType(InstructionType::Unknown),
        conditionCode(ConditionCode::Always), operandCount(0),
        isReadModifyWrite(false), repPrefix(false), repePrefix(false),
        repnePrefix(false), lockPrefix(false), operandSizeOverride(false),
        addressSizeOverride(false), rex(0), isVex(false), isEvex(false) {
    operands.fill(Operand());
  }

  void reset() {
    length = 0;
    pc = 0;
    opcode = 0;
    instType = InstructionType::Unknown;
    conditionCode = ConditionCode::Always;
    operandCount = 0;
    isReadModifyWrite = false;
    repPrefix = false;
    repePrefix = false;
    repnePrefix = false;
    lockPrefix = false;
    operandSizeOverride = false;
    addressSizeOverride = false;
    rex = 0;
    isVex = false;
    isEvex = false;
    for (auto &operand : operands) {
      operand.reset();
    }
  }

  // Validation method
  bool validate() const {
    // Basic validation checks
    if (length == 0 || length > 15)
      return false; // x86-64 instruction length limits
    if (operandCount > 4)
      return false; // Maximum 4 operands
    if (instType == InstructionType::Unknown)
      return false;

    // Validate each operand
    for (uint8_t i = 0; i < operandCount; ++i) {
      const auto &operand = operands[i];

      // Operand size must be valid (non-zero and reasonable)
      if (operand.size == 0 || operand.size > 512) {
        return false; // Invalid operand size
      }

      // Validate operand type consistency
      if (operand.type == Operand::Type::MEMORY) {
        // Validate memory operand scale factor
        if (operand.memory.scale != 1 && operand.memory.scale != 2 &&
            operand.memory.scale != 4 && operand.memory.scale != 8) {
          return false; // Invalid scale factor
        }

        // Validate displacement is reasonable (not checking exact bounds as
        // they're context-dependent) Just ensure it's not an obviously
        // corrupted value
        if (operand.memory.displacement == INT64_MIN) {
          return false; // Likely corrupted displacement
        }
      }

      // Validate register operands
      if (operand.type == Operand::Type::REGISTER ||
          operand.type == Operand::Type::XMM ||
          operand.type == Operand::Type::YMM ||
          operand.type == Operand::Type::ZMM) {
        // Basic register validation - ensure it's not an invalid enum value
        // Use NONE as the upper bound since it's the last valid register
        if (static_cast<int>(operand.reg) < 0 ||
            operand.reg == Register::NONE) {
          return false; // Invalid register
        }
      }
    }

    return true;
  }
};

} // namespace x86_64

#endif // DECODED_INSTRUCTION_H

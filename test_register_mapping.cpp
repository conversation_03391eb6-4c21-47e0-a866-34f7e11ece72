#include "cpu/register.h"
#include <iostream>
#include <cassert>

using namespace x86_64;

// Simple test to verify register enum values
void test_register_mapping() {
    std::cout << "Testing register enum values:\n";
    
    // Test 64-bit registers
    std::cout << "RAX = " << static_cast<int>(Register::RAX) << std::endl;
    std::cout << "RCX = " << static_cast<int>(Register::RCX) << std::endl;
    std::cout << "RIP = " << static_cast<int>(Register::RIP) << std::endl;
    
    // Test 32-bit registers
    std::cout << "EAX = " << static_cast<int>(Register::EAX) << std::endl;
    std::cout << "ECX = " << static_cast<int>(Register::ECX) << std::endl;
    
    // Test 16-bit registers
    std::cout << "AX = " << static_cast<int>(Register::AX) << std::endl;
    std::cout << "CX = " << static_cast<int>(Register::CX) << std::endl;
    
    // Test 8-bit registers
    std::cout << "AL = " << static_cast<int>(Register::AL) << std::endl;
    std::cout << "AH = " << static_cast<int>(Register::AH) << std::endl;
    
    // Verify the mapping logic
    assert(static_cast<int>(Register::RAX) == 0);
    assert(static_cast<int>(Register::RIP) == 16);
    assert(static_cast<int>(Register::EAX) == 17); // This should map to RAX (0)
    
    std::cout << "Register mapping test completed successfully!\n";
}

int main() {
    test_register_mapping();
    return 0;
}

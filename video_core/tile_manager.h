// FileName: MultipleFiles/tile_manager.h
// FileContents:
#pragma once

#include <array>
#include <chrono>
#include <cstdint>
#include <istream>
#include <memory>
#include <ostream>
#include <shared_mutex>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>
#include <algorithm> // For std::min, std::max, std::remove

namespace ps4 {

class CommandProcessor;
class GNMRegisterState;

/**
 * @brief Exception thrown by TileManager for invalid operations.
 */
struct TileManagerException : std::runtime_error {
  explicit TileManagerException(const std::string &msg)
      : std::runtime_error(msg) {}
};

// Tile dimensions
constexpr uint32_t TILE_WIDTH =
    8; ///< Width of a tile in pixels (using 8x8 for GCN architecture)
constexpr uint32_t TILE_HEIGHT =
    8; ///< Height of a tile in pixels (using 8x8 for GCN architecture)

/**
 * @brief Tile modes for surface organization.
 */
enum class TileMode : uint8_t {
  LINEAR = 0,     ///< Linear memory layout
  TILED_1D = 1,   ///< 1D tiled layout (thin)
  TILED_2D = 2,   ///< 2D tiled layout (thick)
  TILED_2B = 3,   ///< 2B tiled layout (2D with bank/pipe rotation)
  TILED_3D = 4,   ///< 3D tiled layout
  TILED_DEPTH = 5 ///< Depth tiled layout
};

/**
 * @brief Tile formats for pixel data.
 */
enum class TileFormat : uint8_t {
  INVALID = 0,
  R8_UNORM = 1,
  R8G8_UNORM = 2,
  R8G8B8A8_UNORM = 3,
  B8G8R8A8_UNORM = 4,
  R16_FLOAT = 5,
  R16G16_FLOAT = 6,
  R16G16B16A16_FLOAT = 7,
  R32_FLOAT = 8,
  R32G32_FLOAT = 9,
  R32G32B32A32_FLOAT = 10,
  D16_UNORM = 11,
  D24_UNORM_S8_UINT = 12,
  D32_FLOAT = 13
};

/**
 * @brief Tile descriptor for tile allocation.
 */
struct TileDescriptor {
  uint32_t width;         ///< Tile width in pixels
  uint32_t height;        ///< Tile height in pixels
  uint32_t bits_per_pixel; ///< Bits per pixel (redundant with format, but kept for flexibility)
  TileFormat format;      ///< Pixel format
  TileMode mode;          ///< Tiling mode
};

/**
 * @brief Tile information structure with metrics.
 */
struct TileInfo {
  uint32_t width;         ///< Surface width in pixels
  uint32_t height;        ///< Surface height in pixels
  uint32_t depth;         ///< Surface depth (for 3D)
  TileMode mode;          ///< Tiling mode
  TileFormat format;      ///< Pixel format
  uint32_t bytesPerPixel; ///< Bytes per pixel (derived from format)
  uint32_t pitch;         ///< Pitch in bytes (row stride)
  uint32_t slice;         ///< Slice size in bytes (for 3D textures, or total size for 2D)
  uint64_t gpuAddr;       ///< GPU memory address
  uint64_t cpuAddr;       ///< CPU memory address (for mapped memory)
  bool isRenderTarget;    ///< Is this a render target?
  bool isDepthStencil;    ///< Is this a depth/stencil buffer?
  mutable uint64_t cacheHits;    ///< Number of times this TileInfo was accessed
  mutable uint64_t cacheMisses;  ///< Number of times this TileInfo was not found (less applicable here)

  /**
   * @brief Serializes the tile info to a stream.
   * @param out Output stream.
   * @details Writes all fields including metrics.
   */
  void Serialize(std::ostream &out) const;

  /**
   * @brief Deserializes the tile info from a stream.
   * @param in Input stream.
   * @throws TileManagerException on deserialization errors.
   */
  void Deserialize(std::istream &in);
};

/**
 * @brief Statistics for tile manager operations.
 */
struct TileManagerStats {
  uint64_t operationCount = 0; ///< Total operations performed
  uint64_t totalLatencyUs = 0; ///< Total latency in microseconds
  uint64_t cacheHits = 0;      ///< Cache hits for surface operations (overall manager cache)
  uint64_t cacheMisses = 0;    ///< Cache misses for surface operations (overall manager cache)
  uint64_t errorCount = 0;     ///< Total errors encountered
};

/**
 * @brief Cache entry for tiled surface data.
 */
struct SurfaceCacheEntry {
  uint64_t surfaceId;
  std::vector<uint8_t> data;
  mutable uint64_t cacheHits = 0;    ///< Number of times this specific cache entry was hit
  mutable uint64_t cacheMisses = 0;  ///< Number of times this specific cache entry was missed (less applicable for direct map)
};

/**
 * @brief Manages tiled surfaces for GPU rendering.
 * @details Handles creation, manipulation, and conversion of tiled surfaces,
 * with thread-safe operations. Integrates with GNMRegisterState for
 * render target registers, CommandProcessor for PM4 packet processing,
 * and ShaderEmulator for tiled surface access. Supports surface
 * caching, serialization with versioning, and multi-core diagnostics.
 */
class TileManager {
public:
  TileManager(GNMRegisterState &gnmState, CommandProcessor &commandProcessor);

  // Delete copy and move operations since we hold references and manage resources
  TileManager(const TileManager &) = delete;
  TileManager &operator=(const TileManager &) = delete;
  TileManager(TileManager &&) = delete;
  TileManager &operator=(TileManager &&) = delete;

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe.
   */
  ~TileManager();

  /**
   * @brief Initializes the tile manager.
   * @return True on success, false on failure.
   * @throws TileManagerException on initialization errors.
   * @details Resets surfaces and metrics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the tile manager.
   * @details Clears surfaces and metrics. Thread-safe.
   */
  void Shutdown();

  /**
   * @brief Creates a tiled surface.
   * @param width Surface width in pixels.
   * @param height Surface height in pixels.
   * @param depth Surface depth (for 3D).
   * @param mode Tiling mode.
   * @param format Pixel format.
   * @param isRenderTarget Is this a render target?
   * @param isDepthStencil Is this a depth/stencil buffer?
   * @return Surface ID.
   * @throws TileManagerException on invalid parameters.
   * @details Thread-safe. Updates metrics and cache.
   */
  uint64_t CreateTiledSurface(uint32_t width, uint32_t height, uint32_t depth,
                              TileMode mode, TileFormat format,
                              bool isRenderTarget = false,
                              bool isDepthStencil = false);

  /**
   * @brief Destroys a tiled surface.
   * @param surfaceId Surface ID.
   * @throws TileManagerException on invalid surface ID.
   * @details Thread-safe. Updates metrics and cache.
   */
  void DestroyTiledSurface(uint64_t surfaceId);

  /**
   * @brief Gets tile information for a surface.
   * @param surfaceId Surface ID.
   * @return Pointer to tile info, or nullptr if invalid.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  const TileInfo *GetTileInfo(uint64_t surfaceId) const;

  /**
   * @brief Converts linear data to tiled format.
   * @param surfaceId Surface ID.
   * @param linearData Input linear data.
   * @param linearSize Size of linear data.
   * @throws TileManagerException on invalid parameters.
   * @details Thread-safe. Updates surface data and cache.
   */
  void LinearToTiled(uint64_t surfaceId, const void *linearData,
                     size_t linearSize);

  /**
   * @brief Converts tiled data to linear format.
   * @param surfaceId Surface ID.
   * @param linearData Output linear data.
   * @param linearSize Size of linear buffer.
   * @throws TileManagerException on invalid parameters.
   * @details Thread-safe (read-only). Reads from surface data.
   */
  void TiledToLinear(uint64_t surfaceId, void *linearData,
                     size_t linearSize) const;

  /**
   * @brief Clears a tiled surface with a color.
   * @param surfaceId Surface ID.
   * @param color RGBA color values (0.0-1.0).
   * @throws TileManagerException on invalid surface ID or unsupported format.
   * @details Thread-safe. Updates cache and metrics.
   */
  void ClearTiledSurface(uint64_t surfaceId, const float color[4]);

  /**
   * @brief Copies one tiled surface to another.
   * @param dstSurfaceId Destination surface ID.
   * @param srcSurfaceId Source surface ID.
   * @throws TileManagerException on invalid or incompatible surfaces.
   * @details Thread-safe. Updates cache and metrics.
   */
  void CopyTiledSurface(uint64_t dstSurfaceId, uint64_t srcSurfaceId);

  /**
   * @brief Gets bytes per pixel for a format.
   * @param format Pixel format.
   * @return Bytes per pixel.
   * @throws TileManagerException on invalid format.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  uint32_t GetBytesPerPixel(TileFormat format);

  /**
   * @brief Gets the current tile's position and size for the active render target.
   * @param x Output tile x-coordinate (pixel).
   * @param y Output tile y-coordinate (pixel).
   * @param width Output tile width (pixel).
   * @param height Output tile height (pixel).
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  void GetCurrentTile(uint32_t &x, uint32_t &y, uint32_t &width,
                      uint32_t &height) const;

  /**
   * @brief Advances to the next tile for the active render target.
   * @details Thread-safe. Updates metrics.
   */
  void NextTile();

  /**
   * @brief Gets the total number of tiles across all active surfaces.
   * @return Total tile count.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  size_t GetTotalTiles() const;

  /**
   * @brief Retrieves cached surface data.
   * @param surfaceId Surface ID.
   * @param data Output cached data (if available).
   * @return True if cached, false otherwise.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  bool GetCachedSurfaceData(uint64_t surfaceId,
                            std::vector<uint8_t> &data) const;

  /**
   * @brief Clears the surface data cache.
   * @details Thread-safe. Resets cache metrics.
   */
  void ClearSurfaceCache();

  /**
   * @brief Tile statistics structure for internal tile management.
   */
  struct TileStats {
    size_t total_tiles = 0;     ///< Total number of tiles capacity
    size_t allocated_tiles = 0; ///< Number of currently allocated tiles
    size_t free_tiles = 0;      ///< Number of free tile slots
    size_t memory_used = 0;     ///< Memory used by allocated tiles in bytes
  };

  /**
   * @brief Retrieves tile manager statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only).
   */
  TileManagerStats GetStats() const;

  /**
   * @brief Gets internal tile allocation statistics.
   * @return Current tile statistics.
   * @details Thread-safe (read-only).
   */
  TileStats GetTileStats() const;

  /**
   * @brief Allocates a tile with the given descriptor.
   * @param tile_id Unique identifier for the tile.
   * @param descriptor Tile descriptor specifying dimensions, format, and mode.
   * @return True on success, false on failure (e.g., tile_id out of bounds, already allocated).
   * @details This is for a more granular tile allocation system, separate from `CreateTiledSurface`.
   */
  bool AllocateTile(uint32_t tile_id, const TileDescriptor& descriptor);

  /**
   * @brief Deallocates a tile.
   * @param tile_id Tile identifier.
   * @return True on success, false on failure (e.g., tile not allocated).
   */
  bool DeallocateTile(uint32_t tile_id);

  /**
   * @brief Writes data to a specific tile.
   * @param tile_id Tile identifier.
   * @param data Pointer to the data to write.
   * @param size Size of data in bytes.
   * @param offset Offset within the tile's data buffer.
   * @return True on success, false on failure (e.g., tile not allocated, out of bounds write).
   */
  bool WriteTileData(uint32_t tile_id, const void* data, size_t size, size_t offset);

  /**
   * @brief Reads data from a specific tile.
   * @param tile_id Tile identifier.
   * @param data Buffer to read into.
   * @param size Size of data to read in bytes.
   * @param offset Offset within the tile's data buffer.
   * @return True on success, false on failure (e.g., tile not allocated, out of bounds read).
   */
  bool ReadTileData(uint32_t tile_id, void* data, size_t size, size_t offset) const;

  /**
   * @brief Gets a list of currently active (allocated) tile IDs.
   * @return Vector of active tile IDs.
   */
  std::vector<uint32_t> GetActiveTiles() const;

  /**
   * @brief Cleans up unused tiles based on their last access time.
   * @param max_age Tiles older than this duration will be deallocated.
   */
  void CleanupUnusedTiles(std::chrono::milliseconds max_age);

  /**
   * @brief Saves the tile manager state to a stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes with versioning (version 1).
   * Includes both surface and internal tile states.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the tile manager state from a stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format.
   * @throws TileManagerException on invalid state data or version mismatch.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Sets the tile mode for an existing surface, re-tiling its data.
   * @param surfaceId Surface ID.
   * @param tileMode New tile mode to set.
   * @return True on success, false on failure.
   * @throws TileManagerException on invalid surface ID or mode, or re-tiling failure.
   * @details Thread-safe. This operation involves converting data to linear,
   * changing the mode, and converting back to the new tiled format.
   */
  bool SetTileMode(uint64_t surfaceId, uint32_t tileMode);

  /**
   * @brief Maps a region of CPU memory to a new tiled GPU surface.
   * @param cpuAddress The base CPU address of the memory to be mapped. This will also serve as the surface ID.
   * @param size The size of the memory region to map in bytes.
   * @param tileMode The desired tiling mode for the new GPU surface.
   * @return The GPU address (which is the same as the surface ID) of the newly mapped tiled memory.
   * @throws TileManagerException on invalid parameters or if mapping fails.
   * @details This function creates a new `TiledSurface` and associates it with the given `cpuAddress`.
   * The surface's dimensions are calculated to accommodate the `size` in the specified `tileMode`.
   * The `cpuAddress` is used as the `surfaceId` for direct lookup.
   */
  uint64_t MapTiledMemory(uint64_t cpuAddress, size_t size, uint32_t tileMode);

private:
  /**
   * @brief Tiled surface structure with metrics.
   */
  struct TiledSurface {
    TileInfo info;                    ///< Surface metadata
    std::vector<uint8_t> data;        ///< Raw surface data (tiled or linear)
    bool active = false;              ///< Is the surface currently active and valid?
    mutable uint64_t cacheHits = 0;   ///< Cache hits for this surface's data
    mutable uint64_t cacheMisses = 0; ///< Cache misses for this surface's data

    /**
     * @brief Serializes the tiled surface to a stream.
     * @param out Output stream.
     */
    void Serialize(std::ostream &out) const;

    /**
     * @brief Deserializes the tiled surface from a stream.
     * @param in Input stream.
     * @throws TileManagerException on deserialization errors.
     */
    void Deserialize(std::istream &in);
  };

  GNMRegisterState &m_gnmState;         ///< Reference to GNM register state
  CommandProcessor &m_commandProcessor; ///< Reference to command processor
  std::unordered_map<uint64_t, TiledSurface> m_surfaces; ///< Map of active surfaces by ID
  mutable std::shared_mutex m_surfaceMutex; ///< Mutex for thread safety of m_surfaces and main state
  uint64_t m_nextSurfaceId = 1;             ///< Counter for next available surface ID
  uint64_t m_currentSurfaceId = 0;          ///< ID of the currently bound render target surface
  uint32_t m_currentTileX = 0;              ///< Current tile X coordinate for render target iteration
  uint32_t m_currentTileY = 0;              ///< Current tile Y coordinate for render target iteration
  uint32_t m_tilesX = 0;                    ///< Total number of tiles in X for the current render target
  uint32_t m_tilesY = 0;                    ///< Total number of tiles in Y for the current render target
  mutable TileManagerStats m_stats;         ///< Overall statistics for the TileManager
  std::unordered_map<uint64_t, SurfaceCacheEntry>
      m_surfaceCache; ///< Cache for frequently accessed surface data (can be redundant with m_surfaces.data)

  // Internal tile management (for AllocateTile/DeallocateTile)
  struct Tile {
    std::vector<uint8_t> data;
    TileDescriptor descriptor;
    bool allocated = false;
    std::chrono::steady_clock::time_point last_access; // For LRU/cleanup
  };

  std::vector<Tile> m_tiles;                ///< Vector storing individual tile data and metadata
  // m_tile_cache is not used in the provided code, consider removing or implementing
  std::unordered_map<uint32_t, Tile> m_tile_cache; ///< Placeholder for a tile cache (e.g., for specific tile IDs)
  std::vector<uint32_t> m_active_tiles;     ///< List of currently active tile IDs (indices into m_tiles)
  size_t m_max_tiles = 1024;               ///< Maximum number of tiles that can be allocated via AllocateTile
  mutable std::shared_mutex m_mutex;       ///< Mutex for thread safety of m_tiles and related internal tile operations

  // Tiling conversion helpers
  void LinearToTiled1D(const TileInfo &info, const void *linearData,
                       void *tiledData) const;
  void TiledToLinear1D(const TileInfo &info, const void *tiledData,
                       void *linearData) const;
  void LinearToTiled2D(const TileInfo &info, const void *linearData,
                       void *tiledData) const;
  void TiledToLinear2D(const TileInfo &info, const void *tiledData,
                       void *linearData) const;
  void LinearToTiled2B(const TileInfo &info, const void *linearData,
                       void *tiledData) const;
  void TiledToLinear2B(const TileInfo &info, const void *tiledData,
                       void *linearData) const;
  void LinearToTiled3D(const TileInfo &info, const void *linearData,
                       void *tiledData) const;
  void TiledToLinear3D(const TileInfo &info, const void *tiledData,
                       void *linearData) const;
  void LinearToTiledDepth(const TileInfo &info, const void *linearData,
                          void *tiledData) const;
  void TiledToLinearDepth(const TileInfo &info, const void *tiledData,
                          void *linearData) const;
  /**
   * @brief Calculates the GCN-style tiled memory address for a given pixel.
   * @param info TileInfo of the surface.
   * @param x X-coordinate of the pixel.
   * @param y Y-coordinate of the pixel.
   * @param z Z-coordinate of the pixel (for 3D textures, 0 for 2D).
   * @return The byte offset from the start of the surface data.
   * @details This is a simplified emulation of GCN's complex tiling. It uses
   * a Z-order (Morton) curve within fixed 8x8 tiles.
   */
  uint64_t GetGcnTiledAddress(const TileInfo &info, uint32_t x, uint32_t y,
                              uint32_t z) const;

  /**
   * @brief Calculates tile layout parameters (pitch, slice) for a surface.
   * @param info Tile information (updated with pitch and slice).
   * @throws TileManagerException on invalid parameters.
   */
  void CalculateTileLayout(TileInfo &info);

  /**
   * @brief Internal helper to get bytes per pixel without locking.
   * @param format Pixel format.
   * @return Bytes per pixel.
   * @throws TileManagerException on invalid format.
   * @details Used when mutex is already held to avoid deadlock.
   */
  uint32_t GetBytesPerPixelInternal(TileFormat format);

  /**
   * @brief Internal tile deallocation method.
   * @param tile_id Tile identifier.
   * @return True on success, false on failure.
   * @details Must be called with m_mutex held.
   */
  bool DeallocateTileInternal(uint32_t tile_id);
};

} // namespace ps4

// Copyright 2025 <Copyright Owner>

#pragma once

#include "../memory/ps4_mmu.h"
#include "../ps4/ps4_gpu.h"
#include <array>
#include <bitset>
#include <chrono>
#include <cstdint>
#include <functional>
#include <istream>
#include <memory>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

namespace ps4 {

/**
 * @brief Exception for command processor errors.
 */
struct CommandProcessorException : std::runtime_error {
  explicit CommandProcessorException(const std::string &msg)
      : std::runtime_error(msg) {}
};

/**
 * @brief PM4 packet types for GPU command buffers.
 */
enum class PM4PacketType : uint8_t {
  TYPE_0 = 0, ///< Header-only packet
  TYPE_1 = 1, ///< Incremental register writes
  TYPE_2 = 2, ///< NOP/padding
  TYPE_3 = 3  ///< Extended opcode set
};

/**
 * @brief PM4 opcodes for Type-3 packets.
 */
enum class PM4Opcode : uint8_t {
  NOP = 0x10,               ///< No operation
  SET_CONTEXT_REG = 0x29,   ///< Set context register
  SET_SH_REG = 0x2A,        ///< Set shader register
  INDEX_BUFFER_SIZE = 0x33, ///< Set index buffer size
  DRAW_INDEX = 0x38,        ///< Draw indexed geometry
  DISPATCH_DIRECT = 0x15,   ///< Dispatch compute workload
  WAIT_REG_MEM = 0x3C,      ///< Wait for register/memory
  ACQUIRE_MEM = 0x3A,       ///< Acquire memory
  RELEASE_MEM = 0x3B,       ///< Release memory
  CALL = 0x1F,              ///< Call subroutine
  RETURN = 0x20,            ///< Return from subroutine
  INDIRECT_BUFFER = 0x32    ///< Indirect buffer execution
};

/**
 * @brief PM4 packet header structure.
 */
struct PM4Header {
  uint32_t value; ///< Raw header value

  /**
   * @brief Gets the packet type.
   * @return Packet type.
   */
  PM4PacketType type() const {
    return static_cast<PM4PacketType>((value >> 30) & 0x3);
  }

  /**
   * @brief Gets the packet data count (excluding header).
   * @return Data word count.
   */
  uint16_t count() const { return static_cast<uint16_t>(value & 0x3FFF); }

  /**
   * @brief Gets the opcode for Type-3 packets.
   * @return Opcode.
   */
  PM4Opcode opcode() const {
    return static_cast<PM4Opcode>((value >> 8) & 0xFF);
  }

  /**
   * @brief Gets the predicate flag.
   * @return Predicate value (0 or 1).
   */
  uint8_t predicate() const {
    return static_cast<uint8_t>((value >> 28) & 0x1);
  }

  /**
   * @brief Gets the shader type for shader-related packets.
   * @return Shader type index.
   */
  uint8_t shaderType() const {
    return static_cast<uint8_t>((value >> 16) & 0xF);
  }

  /**
   * @brief Generates a cache key for the packet.
   * @return Cache key based on header value.
   */
  uint64_t cacheKey() const { return static_cast<uint64_t>(value); }
};

/**
 * @brief Register write packet for Type-1.
 */
struct PM4RegisterWrite {
  uint32_t baseAddress;         ///< Base register address
  std::vector<uint32_t> values; ///< Register values
  uint64_t cacheHits = 0;       ///< Cache hits for this packet
  uint64_t cacheMisses = 0;     ///< Cache misses for this packet
};

/**
 * @brief Draw index packet for Type-3.
 */
struct PM4DrawIndexPacket {
  uint32_t indexCount;      ///< Number of indices
  uint64_t indexBufferBase; ///< Index buffer base address
  uint32_t indexBufferSize; ///< Index buffer size
  uint64_t cacheHits = 0;   ///< Cache hits for this packet
  uint64_t cacheMisses = 0; ///< Cache misses for this packet
};

/**
 * @brief Dispatch compute packet for Type-3.
 */
struct PM4DispatchPacket {
  uint32_t threadGroupX;    ///< Thread group count X
  uint32_t threadGroupY;    ///< Thread group count Y
  uint32_t threadGroupZ;    ///< Thread group count Z
  uint64_t cacheHits = 0;   ///< Cache hits for this packet
  uint64_t cacheMisses = 0; ///< Cache misses for this packet
};

/**
 * @brief Wait register/memory packet for Type-3.
 */
struct PM4WaitRegMemPacket {
  enum class Function : uint32_t {
    ALWAYS = 0,
    LESS_THAN = 1,
    LESS_THAN_EQUAL = 2,
    EQUAL = 3,
    NOT_EQUAL = 4,
    GREATER_THAN_EQUAL = 5,
    GREATER_THAN = 6
  };

  enum class MemSpace : uint32_t { REGISTER = 0, MEMORY = 1 };

  Function function;        ///< Comparison function
  MemSpace memSpace;        ///< Register or memory
  uint64_t pollAddress;     ///< Address to poll
  uint32_t reference;       ///< Reference value
  uint32_t mask;            ///< Value mask
  uint32_t pollInterval;    ///< Polling interval
  uint64_t cacheHits = 0;   ///< Cache hits for this packet
  uint64_t cacheMisses = 0; ///< Cache misses for this packet
};

/**
 * @brief Indirect buffer packet for Type-3.
 */
struct PM4IndirectBufferPacket {
  uint64_t bufferAddress;   ///< Buffer address
  uint32_t bufferSize;      ///< Buffer size in dwords
  uint64_t cacheHits = 0;   ///< Cache hits for this packet
  uint64_t cacheMisses = 0; ///< Cache misses for this packet
};

/**
 * @brief Cache entry for processed PM4 packets.
 */
struct PacketCacheEntry {
  PM4Header header;
  std::vector<uint32_t> data;
  bool processed;
  mutable uint32_t cacheHits{0};
  mutable uint32_t cacheMisses{0};
};

/**
 * @brief Vertex attribute descriptor for pipeline state tracking.
 */
struct VertexAttributeDescriptor {
  uint32_t binding = 0;  ///< Vertex buffer binding index
  uint32_t offset = 0;   ///< Offset within vertex buffer
  uint32_t format = 0;   ///< Attribute format (PS4 format)
  uint32_t location = 0; ///< Shader input location
  bool enabled = false;  ///< Whether attribute is enabled
};

/**
 * @brief Vertex buffer binding descriptor.
 */
struct VertexBufferBinding {
  uint64_t address = 0; ///< GPU address of vertex buffer
  uint32_t stride = 0;  ///< Vertex stride in bytes
  uint32_t size = 0;    ///< Buffer size in bytes
  bool enabled = false; ///< Whether binding is active
};

/**
 * @brief Render target state descriptor.
 */
struct RenderTargetState {
  uint64_t surfaceId = 0; ///< Surface identifier
  uint32_t format = 0;    ///< Surface format
  uint32_t width = 0;     ///< Surface width
  uint32_t height = 0;    ///< Surface height
  uint32_t pitch = 0;     ///< Surface pitch
  bool enabled = false;   ///< Whether render target is bound
  bool dirty = true;      ///< Whether state needs update
};

/**
 * @brief Depth/stencil state descriptor.
 */
struct DepthStencilState {
  bool depthTestEnable = false;       ///< Depth testing enabled
  bool depthWriteEnable = false;      ///< Depth writing enabled
  uint32_t depthCompareOp = 0;        ///< Depth comparison function
  bool stencilTestEnable = false;     ///< Stencil testing enabled
  uint32_t stencilFailOp = 0;         ///< Stencil fail operation
  uint32_t stencilPassOp = 0;         ///< Stencil pass operation
  uint32_t stencilDepthFailOp = 0;    ///< Stencil depth fail operation
  uint32_t stencilCompareOp = 0;      ///< Stencil comparison function
  uint32_t stencilCompareMask = 0xFF; ///< Stencil compare mask
  uint32_t stencilWriteMask = 0xFF;   ///< Stencil write mask
  uint32_t stencilReference = 0;      ///< Stencil reference value
  bool dirty = true;                  ///< Whether state needs update
};

/**
 * @brief Blend state descriptor for a single render target.
 */
struct BlendTargetState {
  bool blendEnable = false;         ///< Blending enabled
  uint32_t srcColorBlendFactor = 0; ///< Source color blend factor
  uint32_t dstColorBlendFactor = 0; ///< Destination color blend factor
  uint32_t colorBlendOp = 0;        ///< Color blend operation
  uint32_t srcAlphaBlendFactor = 0; ///< Source alpha blend factor
  uint32_t dstAlphaBlendFactor = 0; ///< Destination alpha blend factor
  uint32_t alphaBlendOp = 0;        ///< Alpha blend operation
  uint32_t colorWriteMask = 0xF;    ///< Color write mask (RGBA)
};

/**
 * @brief Complete blend state for all render targets.
 */
struct BlendState {
  std::array<BlendTargetState, 8> targets; ///< Per-target blend state
  bool logicOpEnable = false;              ///< Logic operation enabled
  uint32_t logicOp = 0;                    ///< Logic operation
  std::array<float, 4> blendConstants = {0.0f, 0.0f, 0.0f,
                                         0.0f}; ///< Blend constants
  bool dirty = true;                            ///< Whether state needs update
};

/**
 * @brief Rasterizer state descriptor.
 */
struct RasterizerState {
  uint32_t fillMode = 0;                ///< Fill mode (solid, wireframe, point)
  uint32_t cullMode = 0;                ///< Cull mode (none, front, back)
  uint32_t frontFace = 0;               ///< Front face winding order
  bool depthBiasEnable = false;         ///< Depth bias enabled
  float depthBiasConstantFactor = 0.0f; ///< Depth bias constant factor
  float depthBiasClamp = 0.0f;          ///< Depth bias clamp
  float depthBiasSlopeFactor = 0.0f;    ///< Depth bias slope factor
  bool depthClampEnable = false;        ///< Depth clamping enabled
  bool rasterizerDiscardEnable = false; ///< Rasterizer discard enabled
  uint32_t polygonMode = 0;             ///< Polygon mode
  float lineWidth = 1.0f;               ///< Line width
  bool dirty = true;                    ///< Whether state needs update
};

/**
 * @brief Viewport state descriptor.
 */
struct ViewportState {
  float x = 0.0f;        ///< Viewport X offset
  float y = 0.0f;        ///< Viewport Y offset
  float width = 0.0f;    ///< Viewport width
  float height = 0.0f;   ///< Viewport height
  float minDepth = 0.0f; ///< Minimum depth value
  float maxDepth = 1.0f; ///< Maximum depth value
  bool dirty = true;     ///< Whether state needs update
};

/**
 * @brief Scissor test state descriptor.
 */
struct ScissorState {
  int32_t x = 0;        ///< Scissor rectangle X offset
  int32_t y = 0;        ///< Scissor rectangle Y offset
  uint32_t width = 0;   ///< Scissor rectangle width
  uint32_t height = 0;  ///< Scissor rectangle height
  bool enabled = false; ///< Whether scissor test is enabled
  bool dirty = true;    ///< Whether state needs update
};

/**
 * @brief Index buffer state descriptor.
 */
struct IndexBufferState {
  uint64_t address = 0; ///< GPU address of index buffer
  uint32_t size = 0;    ///< Buffer size in bytes
  uint32_t format = 0;  ///< Index format (16-bit or 32-bit)
  bool enabled = false; ///< Whether index buffer is bound
  bool dirty = true;    ///< Whether state needs update
};

/**
 * @brief Shader stage state descriptor.
 */
struct ShaderStageState {
  uint64_t shaderId = 0;    ///< Shader identifier
  uint64_t codeAddress = 0; ///< Shader code address
  uint32_t codeSize = 0;    ///< Shader code size
  bool enabled = false;     ///< Whether shader is bound
  bool dirty = true;        ///< Whether state needs update
};

/**
 * @brief Complete graphics pipeline state tracking.
 */
struct GraphicsPipelineState {
  // Vertex input state
  std::array<VertexAttributeDescriptor, 16>
      vertexAttributes;                              ///< Vertex attributes
  std::array<VertexBufferBinding, 16> vertexBuffers; ///< Vertex buffer bindings
  uint32_t activeVertexAttributes = 0; ///< Bitmask of active attributes
  uint32_t activeVertexBuffers = 0;    ///< Bitmask of active buffers

  // Index buffer state
  IndexBufferState indexBuffer; ///< Index buffer state

  // Shader stages
  std::array<ShaderStageState, 6>
      shaderStages; ///< Shader stages (VS, HS, DS, GS, PS, CS)

  // Render targets
  std::array<RenderTargetState, 8> colorTargets; ///< Color render targets
  RenderTargetState depthTarget;                 ///< Depth/stencil target
  uint32_t activeColorTargets = 0; ///< Bitmask of active color targets

  // Pipeline state
  DepthStencilState depthStencil; ///< Depth/stencil state
  BlendState blend;               ///< Blend state
  RasterizerState rasterizer;     ///< Rasterizer state
  ViewportState viewport;         ///< Viewport state
  ScissorState scissor;           ///< Scissor state

  // Primitive topology
  uint32_t primitiveTopology = 0; ///< Primitive topology

  // State tracking
  bool pipelineDirty = true;   ///< Whether pipeline needs rebuild
  bool renderPassDirty = true; ///< Whether render pass needs rebuild
  uint64_t stateHash = 0;      ///< Hash of current state
  std::chrono::steady_clock::time_point lastUpdate; ///< Last update timestamp
};

/**
 * @brief Command dependency tracking for proper execution order.
 */
struct CommandDependency {
  enum Type {
    MEMORY_READ,     ///< Command reads from memory
    MEMORY_WRITE,    ///< Command writes to memory
    RENDER_TARGET,   ///< Command uses render target
    SHADER_RESOURCE, ///< Command uses shader resource
    PIPELINE_STATE   ///< Command modifies pipeline state
  };

  Type type;           ///< Dependency type
  uint64_t resourceId; ///< Resource identifier
  uint64_t address;    ///< Memory address (for memory dependencies)
  uint32_t size;       ///< Size in bytes (for memory dependencies)
  bool isWrite;        ///< Whether dependency is a write operation
};

/**
 * @brief Command execution context for tracking dependencies and state.
 */
struct CommandExecutionContext {
  std::vector<CommandDependency> dependencies; ///< Command dependencies
  uint64_t commandId;                          ///< Unique command identifier
  std::chrono::steady_clock::time_point timestamp; ///< Command timestamp
  uint32_t estimatedCycles; ///< Estimated execution cycles
  bool requiresSync;        ///< Whether command requires synchronization
};

/**
 * @brief Enhanced PM4 opcodes for comprehensive coverage.
 */
enum class EnhancedPM4Opcode : uint8_t {
  // Drawing commands
  DRAW_INDEX = 0x10,
  DRAW_INDEX_AUTO = 0x11,
  DRAW_INDIRECT = 0x12,
  DRAW_INDEX_INDIRECT = 0x13,
  DRAW_INDEX_MULTI = 0x14,

  // State setting commands
  SET_CONTEXT_REG = 0x69,
  SET_CONFIG_REG = 0x68,
  SET_SH_REG = 0x76,
  SET_UCONFIG_REG = 0x79,
  LOAD_CONTEXT_REG = 0x6A,
  LOAD_SH_REG = 0x77,

  // Memory operations
  DMA_DATA = 0x50,
  COPY_DATA = 0x40,
  WRITE_DATA = 0x37,
  READ_DATA = 0x38,

  // Synchronization
  EVENT_WRITE = 0x46,
  EVENT_WRITE_EOP = 0x47,
  WAIT_REG_MEM = 0x3C,
  SURFACE_SYNC = 0x49,

  // Compute
  DISPATCH_DIRECT = 0x15,
  DISPATCH_INDIRECT = 0x16,

  // Resource management
  ACQUIRE_MEM = 0x58,
  RELEASE_MEM = 0x59,

  // Texture operations
  SET_SAMPLER = 0x60,
  SET_RESOURCE = 0x61,

  // Shader operations
  SET_SHADER = 0x70,
  LOAD_SHADER = 0x71,

  NOP = 0x10,               ///< No operation
  CALL = 0x1F,              ///< Call subroutine
  RETURN = 0x20,            ///< Return from subroutine
  INDIRECT_BUFFER = 0x32,   ///< Indirect buffer execution
  INDEX_BUFFER_SIZE = 0x33, ///< Set index buffer size

  UNKNOWN = 0xFF
};

/**
 * @brief Statistics for command processor operations.
 */
struct CommandProcessorStats {
  uint64_t packetCount = 0;       ///< Total packets processed
  uint64_t totalLatencyUs = 0;    ///< Total latency in microseconds
  uint64_t cacheHits = 0;         ///< Cache hits for packet processing
  uint64_t cacheMisses = 0;       ///< Cache misses for packet processing
  uint64_t errorCount = 0;        ///< Total errors encountered
  uint64_t drawCalls = 0;         ///< Number of draw calls processed
  uint64_t computeDispatches = 0; ///< Number of compute dispatches
  uint64_t stateChanges = 0;      ///< Number of state changes
  uint64_t memoryOperations = 0;  ///< Number of memory operations
  uint64_t type0Packets = 0;      ///< Type 0 packets processed
  uint64_t type1Packets = 0;      ///< Type 1 packets processed
  uint64_t type2Packets = 0;      ///< Type 2 packets processed
  uint64_t type3Packets = 0;      ///< Type 3 packets processed
};

/**
 * @brief Processes PM4 command buffers for the PS4 GPU.
 * @details Manages command buffer processing in a thread-safe manner, with
 *          caching to reduce redundant GPU calls. Integrates with PS4MMU for
 *          memory access and PS4GPU for rendering. Supports serialization with
 *          versioning and multi-core diagnostics.
 */
class CommandProcessor {
public:
  /**
   * @brief Constructs the command processor.
   * @param mem PS4 memory management unit.
   * @param gpu PS4 GPU emulator.
   * @details Initializes metrics and cache. Thread-safe.
   */
  explicit CommandProcessor(ps4::PS4MMU &mem, PS4GPU &gpu);

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe and noexcept.
   */
  ~CommandProcessor() noexcept;

  /**
   * @brief Initializes the command processor.
   * @return True on success, false on failure.
   * @throws CommandProcessorException on initialization errors.
   * @details Clears call stack and resets metrics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the command processor.
   * @details Clears call stack and cache. Thread-safe.
   */
  void Shutdown();

  /**
   * @brief Processes a PM4 command buffer.
   * @param addr Virtual address of the buffer.
   * @param size Buffer size in bytes.
   * @throws CommandProcessorException on memory or processing errors.
   * @details Thread-safe. Checks cache first, updates metrics.
   * @note May use GNMRegisterState for register data and TileManager for tiled
   * surfaces.
   */
  void ProcessCommandBuffer(uint64_t addr, size_t size);

  /**
   * @brief Retrieves a cached packet result.
   * @param packetKey Cache key for the packet.
   * @param data Output packet data (if cached).
   * @return True if cached, false otherwise.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  bool GetCachedPacketResult(uint64_t packetKey,
                             std::vector<uint32_t> &data) const;

  /**
   * @brief Clears the PM4 packet cache.
   * @details Thread-safe. Resets cache metrics.
   */
  void ClearPacketCache();

  /**
   * @brief Retrieves command processor statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  CommandProcessorStats GetStats() const;

  /**
   * @brief Saves the command processor state to a stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes with versioning (version 1).
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the command processor state from a stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format.
   * @throws CommandProcessorException on invalid state data.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Reads a 32-bit word from virtual memory.
   * @param address Virtual address.
   * @return 32-bit value.
   * @throws CommandProcessorException on memory read errors.
   * @details Thread-safe. Updates metrics.
   */
  uint32_t ReadDword(uint64_t address);

  /**
   * @brief Reads a 64-bit qword from virtual memory.
   * @param address Virtual address.
   * @return 64-bit value.
   * @throws CommandProcessorException on memory read errors.
   * @details Thread-safe. Updates metrics.
   */
  uint64_t ReadQword(uint64_t address);

  /**
   * @brief Notifies command processor that a surface was created.
   * @param surfaceId Surface identifier.
   * @param width Surface width.
   * @param height Surface height.
   * @param format Surface format.
   * @details Updates surface tracking for command optimization.
   */
  void NotifySurfaceCreated(uint32_t surfaceId, uint32_t width, uint32_t height,
                            uint32_t format);

  /**
   * @brief Notifies command processor that a surface was destroyed.
   * @param surfaceId Surface identifier.
   * @details Cleans up surface tracking data.
   */
  void NotifySurfaceDestroyed(uint32_t surfaceId);

  /**
   * @brief Notifies command processor that a surface was updated.
   * @param surfaceId Surface identifier.
   * @param x X coordinate of updated region.
   * @param y Y coordinate of updated region.
   * @param width Width of updated region.
   * @param height Height of updated region.
   * @details Marks surface region as dirty for GPU sync.
   */
  void NotifySurfaceUpdated(uint32_t surfaceId, uint32_t x, uint32_t y,
                            uint32_t width, uint32_t height);

  /**
   * @brief Notifies command processor that a surface was cleared.
   * @param surfaceId Surface identifier.
   * @param clearValue Clear value used.
   * @details Optimizes subsequent draw operations.
   */
  void NotifySurfaceClear(uint32_t surfaceId, uint32_t clearValue);

  /**
   * @brief Notifies command processor that a surface copy occurred.
   * @param srcSurfaceId Source surface identifier.
   * @param dstSurfaceId Destination surface identifier.
   * @param srcX Source X coordinate.
   * @param srcY Source Y coordinate.
   * @param dstX Destination X coordinate.
   * @param dstY Destination Y coordinate.
   * @param width Copy width.
   * @param height Copy height.
   * @details Updates surface dependencies for optimization.
   */
  void NotifySurfaceCopy(uint32_t srcSurfaceId, uint32_t dstSurfaceId,
                         uint32_t srcX, uint32_t srcY, uint32_t dstX,
                         uint32_t dstY, uint32_t width, uint32_t height);

  /**
   * @brief Notifies command processor that memory was mapped to a surface.
   * @param cpuAddress CPU address of the mapped memory.
   * @param surfaceId Surface identifier.
   * @param size Size of the mapped memory.
   * @param tileMode Tile mode of the mapped surface.
   * @details Updates memory mapping tracking for optimization.
   */
  void NotifyMemoryMapped(uint64_t cpuAddress, uint64_t surfaceId, size_t size,
                          uint32_t tileMode);

  /**
   * @brief Queue entry for deferred indirect buffer processing.
   */
  struct IndirectBufferQueueEntry {
    uint64_t bufferAddress; ///< Buffer address
    uint32_t bufferSize;    ///< Buffer size in dwords
  };

  /**
   * @brief Enhanced command processing methods.
   */
  bool ProcessEnhancedPM4Packet(EnhancedPM4Opcode opcode,
                                const std::vector<uint32_t> &data);
  bool ValidatePacketData(const PM4Header &header,
                          const std::vector<uint32_t> &data);
  void OptimizeCommandSequence(std::vector<PM4Header> &headers,
                               std::vector<std::vector<uint32_t>> &data);
  uint32_t EstimatePacketCycles(const PM4Header &header,
                                const std::vector<uint32_t> &data);

private:
  /**
   * @brief Processes a Type-0 packet (single register write).
   * @param header Packet header.
   * @param data Packet data.
   * @param offset Current offset (updated).
   * @throws CommandProcessorException on invalid packet data.
   * @details Thread-safe. Updates metrics.
   */
  void ProcessType0Packet(const PM4Header &header,
                          const std::vector<uint32_t> &data, uint32_t &offset);

  /**
   * @brief Processes a Type-1 packet (incremental register writes).
   * @param header Packet header.
   * @param data Packet data.
   * @param offset Current offset (updated).
   * @throws CommandProcessorException on invalid packet data.
   * @details Thread-safe. Updates metrics.
   */
  void ProcessType1Packet(const PM4Header &header,
                          const std::vector<uint32_t> &data, uint32_t &offset);

  /**
   * @brief Processes a Type-2 packet (NOP/padding).
   * @param header Packet header.
   * @param offset Current offset (updated).
   * @param data Packet data.
   * @details Thread-safe. Updates metrics.
   */
  void ProcessType2Packet(const PM4Header &header, uint32_t &offset,
                          const std::vector<uint32_t> &data);

  /**
   * @brief Processes a Type-3 packet (extended opcodes).
   * @param header Packet header.
   * @param offset Current offset (updated).
   * @param data Packet data.
   * @throws CommandProcessorException on invalid packet data.
   * @details Thread-safe. Dispatches to specific opcode handlers.
   */
  void ProcessType3Packet(const PM4Header &header, uint32_t &offset,
                          const std::vector<uint32_t> &data);

  /**
   * @brief Processes a NOP packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @details Thread-safe. Updates metrics.
   */
  void ProcessNOP(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a SET_SH_REG packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Updates shader registers via PS4GPU.
   */
  void ProcessSetShaderReg(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a SET_CONTEXT_REG packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Updates context registers via PS4GPU.
   */
  void ProcessSetContextReg(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a DRAW_INDEX packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Issues draw call via PS4GPU.
   */
  void ProcessDrawIndex(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a DISPATCH_DIRECT packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Issues compute dispatch via PS4GPU.
   */
  void ProcessDispatchDirect(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a WAIT_REG_MEM packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Issues wait via PS4GPU.
   */
  void ProcessWaitRegMem(uint64_t &address, uint32_t count);

  /**
   * @brief Processes an ACQUIRE_MEM packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Acquires memory via PS4GPU.
   */
  void ProcessAcquireMem(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a RELEASE_MEM packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Releases memory via PS4GPU.
   */
  void ProcessReleaseMem(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a CALL packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Updates call stack.
   */
  void ProcessCall(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a RETURN packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid call stack.
   * @details Thread-safe. Updates call stack.
   */
  void ProcessReturn(uint64_t &address, uint32_t count);

  /**
   * @brief Processes an INDIRECT_BUFFER packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Recursively processes buffer.
   */
  void ProcessIndirectBuffer(uint64_t &address, uint32_t count);

  /**
   * @brief Processes an INDEX_BUFFER_SIZE packet.
   * @param address Current address (updated).
   * @param count Data word count.
   * @throws CommandProcessorException on invalid data.
   * @details Thread-safe. Updates index buffer size.
   */
  void ProcessIndexBufferSize(uint64_t &address, uint32_t count);

  /**
   * @brief Enhanced packet processing methods.
   */
  void ProcessDrawIndexAuto(uint64_t &address, uint32_t count);
  void ProcessDrawIndirect(uint64_t &address, uint32_t count);
  void ProcessDrawIndexIndirect(uint64_t &address, uint32_t count);
  void ProcessDrawIndexMulti(uint64_t &address, uint32_t count);
  void ProcessSetConfigReg(uint64_t &address, uint32_t count);
  void ProcessSetUConfigReg(uint64_t &address, uint32_t count);
  void ProcessLoadContextReg(uint64_t &address, uint32_t count);
  void ProcessLoadShaderReg(uint64_t &address, uint32_t count);
  void ProcessDMAData(uint64_t &address, uint32_t count);
  void ProcessCopyData(uint64_t &address, uint32_t count);
  void ProcessWriteData(uint64_t &address, uint32_t count);
  void ProcessReadData(uint64_t &address, uint32_t count);
  void ProcessEventWrite(uint64_t &address, uint32_t count);
  void ProcessEventWriteEOP(uint64_t &address, uint32_t count);
  void ProcessSurfaceSync(uint64_t &address, uint32_t count);
  void ProcessDispatchIndirect(uint64_t &address, uint32_t count);
  void ProcessSetSampler(uint64_t &address, uint32_t count);
  void ProcessSetResource(uint64_t &address, uint32_t count);
  void ProcessSetShader(uint64_t &address, uint32_t count);
  void ProcessLoadShader(uint64_t &address, uint32_t count);

  /**
   * @brief Processes a single command from the command buffer.
   * @param command_type Type of the command to process.
   * @param data Command data.
   * @return True on success, false on failure.
   * @details Thread-safe. Dispatches to appropriate command handler.
   */
  bool ProcessSingleCommand(uint32_t command_type, const std::vector<uint32_t>& data);

  ps4::PS4MMU &m_mmu;                         ///< Memory management unit
  PS4GPU &m_gpu;                              ///< GPU emulator
  mutable std::shared_mutex m_processorMutex; ///< Mutex for thread safety
  std::vector<uint64_t> m_callStack;          ///< Call stack for CALL/RETURN
  std::vector<IndirectBufferQueueEntry>
      m_indirectBufferQueue; ///< Deferred indirect buffer queue
  std::unordered_map<uint64_t, PacketCacheEntry>
      m_packetCache;                     ///< PM4 packet cache
  mutable CommandProcessorStats m_stats; ///< Processor statistics

  // Command processing constants and handlers
  static constexpr uint32_t MAX_COMMAND_TYPES = 256; ///< Maximum number of command types
  std::vector<std::function<bool(const std::vector<uint32_t>&)>> m_command_handlers; ///< Command handler dispatch table

  // Enhanced state tracking
  GraphicsPipelineState m_pipelineState; ///< Current graphics pipeline state
  std::vector<CommandExecutionContext>
      m_commandQueue;           ///< Command execution queue
  uint64_t m_nextCommandId = 1; ///< Next command identifier
  std::unordered_map<uint64_t, uint64_t>
      m_resourceDependencies; ///< Resource dependency tracking

  // Command optimization state
  std::vector<CommandExecutionContext>
      m_pendingCommands; ///< Commands pending optimization
  std::unordered_set<uint64_t>
      m_cachedPipelineStates; ///< Cached pipeline state hashes
  uint64_t m_lastStateHash =
      0; ///< Last applied state hash for redundancy elimination

  // Debug and error handling state
  bool m_debugMode = false; ///< Debug mode enabled
  std::unordered_set<uint32_t>
      m_debugBreakpoints; ///< Debug breakpoints on opcodes
  uint32_t m_errorThreshold =
      100; ///< Maximum errors before stopping processing

  // State management methods
  void UpdatePipelineState(); ///< Update pipeline state from registers
  void ValidateDrawState();   ///< Validate state before draw commands
  void TrackCommandDependencies(
      const CommandExecutionContext &context); ///< Track command dependencies
  uint64_t ComputeStateHash() const; ///< Compute hash of current pipeline state
  bool IsStateCompatible(uint64_t hash)
      const; ///< Check if state is compatible with cached pipeline

  // Advanced draw command support
  void SetupVertexBufferBindings(); ///< Setup vertex buffer bindings from state
  void ValidateIndexBuffer(uint64_t address,
                           uint32_t indexCount); ///< Validate index buffer
  void
  ProcessMultiDrawCommands(const std::vector<uint32_t>
                               &drawData); ///< Process multiple draw commands
  bool IsVertexAttributeCompatible(
      uint32_t binding,
      uint32_t format); ///< Check vertex attribute compatibility

  // Register state validation
  bool
  ValidateContextRegister(uint32_t offset,
                          uint32_t value); ///< Validate context register write
  bool
  ValidateShaderRegister(uint32_t stage, uint32_t offset,
                         uint32_t value); ///< Validate shader register write
  bool ValidateRenderTargetState(
      uint32_t index, uint32_t value); ///< Validate render target configuration
  bool ValidateBlendState(uint32_t offset,
                          uint32_t value); ///< Validate blend state register
  bool ValidateDepthStencilState(
      uint32_t offset,
      uint32_t value); ///< Validate depth/stencil state register
  void
  ApplyRegisterConstraints(uint32_t &value, uint32_t offset,
                           bool isShaderReg); ///< Apply PS4 GPU constraints

  // Enhanced memory management integration
  bool ValidateMemoryAccess(uint64_t address, uint32_t size,
                            bool isWrite); ///< Validate memory access
  bool ValidateResourceBinding(uint64_t resourceId,
                               uint32_t binding); ///< Validate resource binding
  void TrackMemoryDependency(uint64_t address, uint32_t size,
                             bool isWrite); ///< Track memory dependency
  bool IsTiledMemoryAddress(
      uint64_t address); ///< Check if address uses tiled memory layout
  uint64_t TranslateTiledAddress(
      uint64_t tiledAddress,
      uint32_t tileMode); ///< Translate tiled to linear address

  // Command buffer optimization
  void OptimizeCommandSequence(); ///< Optimize pending command sequence
  bool CanBatchCommands(const CommandExecutionContext &cmd1,
                        const CommandExecutionContext
                            &cmd2); ///< Check if commands can be batched
  void EliminateRedundantState();   ///< Remove redundant state changes
  void FlushPendingCommands();      ///< Flush all pending optimized commands
  bool IsPipelineStateCached(
      uint64_t stateHash); ///< Check if pipeline state is cached

  // Error handling and debugging
  void
  ValidateCommandBuffer(uint64_t address,
                        uint32_t size); ///< Validate command buffer integrity
  void DumpPipelineState(
      std::ostream &out); ///< Dump current pipeline state for debugging
  void
  DumpCommandQueue(std::ostream &out); ///< Dump command queue for debugging
  void EnableDebugMode(bool enable);   ///< Enable/disable debug mode
  void SetDebugBreakpoint(
      uint32_t opcode); ///< Set debug breakpoint on specific opcode
  bool
  CheckDebugBreakpoint(uint32_t opcode); ///< Check if debug breakpoint is hit
};

} // namespace ps4
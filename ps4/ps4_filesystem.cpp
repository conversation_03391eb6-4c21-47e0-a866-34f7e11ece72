// Copyright 2025 <Copyright Owner>

#include "ps4_filesystem.h"
#include "../common/lock_ordering.h"
#ifdef TESTING_MODE
#include "../tests/utils/ps4_emulator_stub.h"
#else
#include "../memory/memory_diagnostics.h"
#include "ps4_emulator.h"
#endif

// Standard C++ library headers
#include <algorithm>       // For std::min, std::max, etc.
#include <chrono>          // For timing operations
#include <fcntl.h>         // For file control options (open, etc.)
#include <filesystem>      // For filesystem operations
#include <fmt/core.h>      // For formatting (fmt library)
#include <fstream>         // For file streams
#include <iomanip>         // For stream manipulators
#include <openssl/evp.h>   // For EVP encryption (OpenSSL)
#include <openssl/aes.h>   // For AES encryption (OpenSSL)
#include <random>          // For random number generation
#include <spdlog/spdlog.h> // For logging (spdlog library)
#include <sstream>         // For string streams
#include <stdexcept>       // For exception handling
#include <sys/stat.h>      // For file status

// Platform-specific includes and definitions
#ifndef S_ISDIR
#define S_ISDIR(m) (((m) & S_IFMT) == S_IFDIR) // Define S_ISDIR if not present
#endif

#ifdef _WIN32
#include <io.h>    // Windows-specific I/O functions
#define stat _stat // Map stat to _stat for Windows
#ifndef O_ACCMODE
#define O_ACCMODE                                                              \
  (_O_RDONLY | _O_WRONLY | _O_RDWR) // Define O_ACCMODE if missing
#endif
#ifndef S_IWUSR
#define S_IWUSR _S_IWRITE // Define S_IWUSR for Windows
#endif

// Undefine conflicting Windows macros to avoid name conflicts
#ifdef CreateDirectoryA
#undef CreateDirectoryA
#endif
#ifdef CreateDirectoryW
#undef CreateDirectoryW
#endif
#ifdef CreateDirectory
#undef CreateDirectory
#endif
#ifdef RemoveDirectoryA
#undef RemoveDirectoryA
#endif
#ifdef RemoveDirectoryW
#undef RemoveDirectoryW
#endif
#ifdef RemoveDirectory
#undef RemoveDirectory
#endif
#ifdef EncryptFileA
#undef EncryptFileA
#endif
#ifdef EncryptFileW
#undef EncryptFileW
#endif
#ifdef EncryptFile
#undef EncryptFile
#endif
#ifdef DecryptFileA
#undef DecryptFileA
#endif
#ifdef DecryptFileW
#undef DecryptFileW
#endif
#ifdef DecryptFile
#undef DecryptFile
#endif
#else
#include <unistd.h> // Unix-specific functions (e.g., close, read)
#endif

namespace ps4 {

// MountPointManager implementation
bool MountPointManager::Mount(const std::filesystem::path &hostPath,
                              const std::string &guestPath, bool readOnly,
                              bool isPFS) {
  COMPONENT_LOCK(m_mutex, "MountPointMutex");

  // Validate input paths
  if (hostPath.empty() || guestPath.empty()) {
    spdlog::warn("MountPointManager: Cannot mount with empty paths. Host: '{}', Guest: '{}'",
                 hostPath.string(), guestPath);
    return false;
  }

  if (m_mountPoints.count(guestPath)) {
    spdlog::warn(
        "MountPointManager: Mount point {} already exists. Overwriting.",
        guestPath);
  }
  m_mountPoints[guestPath] = {hostPath, readOnly};
  if (isPFS) {
    m_pfsMountPoints[guestPath] = true;
  }
  spdlog::info("Mounted {} to {}", hostPath.string(), guestPath);
  return true;
}

bool MountPointManager::Unmount(const std::string &guestPath) {
  COMPONENT_LOCK(m_mutex, "MountPointMutex");
  if (m_mountPoints.erase(guestPath)) {
    m_pfsMountPoints.erase(guestPath);
    spdlog::info("Unmounted {}", guestPath);
    return true;
  }
  spdlog::warn("MountPointManager: Mount point {} not found.", guestPath);
  return false;
}

void MountPointManager::UnmountAll() {
  COMPONENT_LOCK(m_mutex, "MountPointMutex");
  m_mountPoints.clear();
  m_pfsMountPoints.clear();
  spdlog::info("Unmounted all mount points.");
}

// Helper function to normalize guest paths
std::string NormalizePath(const std::string &path) {
  if (path.empty()) return path;

  std::string normalized = path;

  // Replace multiple consecutive slashes with single slash
  size_t pos = 0;
  while ((pos = normalized.find("//", pos)) != std::string::npos) {
    normalized.replace(pos, 2, "/");
  }

  // Handle "./" components
  pos = 0;
  while ((pos = normalized.find("/./", pos)) != std::string::npos) {
    normalized.replace(pos, 3, "/");
  }

  // Handle trailing "/."
  if (normalized.length() >= 2 && normalized.substr(normalized.length() - 2) == "/.") {
    normalized = normalized.substr(0, normalized.length() - 2);
    if (normalized.empty()) normalized = "/";
  }

  return normalized;
}

std::filesystem::path
MountPointManager::GetHostPath(const std::string &guestPath,
                               bool *isReadOnly) const {
  auto start = std::chrono::steady_clock::now();
  const auto timeout =
      std::chrono::milliseconds(50); // Short timeout for mount point lookup

  try {
    // Normalize the guest path first
    std::string normalizedGuestPath = NormalizePath(guestPath);

    // Use ordered shared lock for mount point access
    COMPONENT_LOCK(m_mutex, "MountPointManager");

    // Iterate through mount points to find the longest matching prefix
    std::string bestMatchGuestPath;
    std::filesystem::path bestMatchHostPath;
    bool bestMatchReadOnly = false;

    uint32_t iterations = 0;
    const uint32_t maxIterations = 1000; // Prevent infinite loops

    for (const auto &pair : m_mountPoints) {
      // Check timeout and iteration limit
      if (++iterations > maxIterations) {
        spdlog::warn("GetHostPath: Too many iterations, breaking loop");
        break;
      }

      auto current = std::chrono::steady_clock::now();
      if (current - start > timeout) {
        spdlog::warn("GetHostPath: Timeout during iteration for '{}'",
                     normalizedGuestPath);
        break;
      }

      const std::string &mountGuestPath = pair.first;
      const std::filesystem::path &mountHostPath = pair.second.first;
      bool mountReadOnly = pair.second.second;

      if (normalizedGuestPath.rfind(mountGuestPath, 0) ==
          0) { // Check if normalizedGuestPath starts with mountGuestPath
        if (mountGuestPath.length() > bestMatchGuestPath.length()) {
          bestMatchGuestPath = mountGuestPath;
          bestMatchHostPath = mountHostPath;
          bestMatchReadOnly = mountReadOnly;
        }
      }
    }

    if (!bestMatchGuestPath.empty()) {
      std::string remainingPath = normalizedGuestPath.substr(bestMatchGuestPath.length());
      // Remove leading slash from remaining path if present
      if (!remainingPath.empty() && remainingPath[0] == '/') {
        remainingPath = remainingPath.substr(1);
      }

      if (isReadOnly) {
        *isReadOnly = bestMatchReadOnly;
      }

      std::filesystem::path result = bestMatchHostPath;
      if (!remainingPath.empty()) {
        result = result / remainingPath;
      }

      spdlog::debug(
          "GetHostPath: Constructed path '{}' from mount '{}' + remaining '{}'",
          result.string(), bestMatchHostPath.string(), remainingPath);
      return result;
    }
  } catch (const std::exception &e) {
    spdlog::error("GetHostPath failed for '{}': {}", guestPath, e.what());
  }

  return {}; // Return empty path if no mount point matches
}

// HandleTable implementation
int HandleTable::CreateHandle() {
  COMPONENT_LOCK(m_mutex, "HandleTableMutex");
  int newFd = m_nextFd++;
  m_files[newFd] = File();   // Initialize a new File struct
  m_files[newFd].fd = newFd; // Set the fd within the File struct
  return newFd;
}

void HandleTable::DeleteHandle(int fd) {
  COMPONENT_LOCK(m_mutex, "HandleTableMutex");
  m_files.erase(fd);
}

HandleTable::File *HandleTable::GetFile(int fd) {
  COMPONENT_SHARED_LOCK(m_mutex, "HandleTableMutex");
  auto it = m_files.find(fd);
  if (it != m_files.end()) {
    return &it->second;
  }
  return nullptr;
}

void HandleTable::CreateStdHandles() {
  COMPONENT_LOCK(m_mutex, "HandleTableMutex");
  // Standard input, output, error
  // These are typically handled by the host OS, but we can represent them
  // as special FDs in our handle table if needed for internal tracking.
  // For now, we'll just ensure m_nextFd starts from 3.
  // If actual host FDs are needed for stdin/out/err, they should be opened
  // and assigned here.
  spdlog::debug("Creating standard handles (FD 0, 1, 2)");
  // Example:
  // m_files[0] = {0, true, "/dev/stdin", "/dev/stdin", PS4FileType::Device};
  // m_files[1] = {1, true, "/dev/stdout", "/dev/stdout", PS4FileType::Device};
  // m_files[2] = {2, true, "/dev/stderr", "/dev/stderr", PS4FileType::Device};
  // m_nextFd = 3; // Ensure next FD starts after standard ones
}

// Constructor with emulator reference
PS4Filesystem::PS4Filesystem(PS4Emulator &emu)
    : m_emulator(emu), m_rootPath(""), m_nextFd(3), m_gameDirectory("") {
  m_stats = FilesystemStats();
  InitializeCache();
  spdlog::info("PS4Filesystem constructed");
}

// Default constructor
PS4Filesystem::PS4Filesystem()
    : m_emulator(PS4Emulator::GetInstance()), m_rootPath(""), m_nextFd(3),
      m_gameDirectory("") {
  m_stats = FilesystemStats();
  InitializeCache();
  spdlog::info("PS4Filesystem constructed (default)");
}

// Destructor
PS4Filesystem::~PS4Filesystem() {
  ShutdownCache();
  Shutdown();
  spdlog::info("PS4Filesystem destroyed");
}

// Initialize the filesystem
bool PS4Filesystem::Initialize() {
  auto start = std::chrono::steady_clock::now();
  const auto overallTimeout = std::chrono::seconds(
      180); // 3 minute overall timeout for filesystem initialization

  // Use explicit scope for the filesystem lock to ensure it's released before
  // calling external diagnostics
  {
    COMPONENT_LOCK(m_mutex, "PS4Filesystem");
    try {
      m_files.clear();
      m_fileHandles.clear();
      m_directories.clear();
      m_deviceFiles.clear();
      m_nextFd = 3;
      m_stats = FilesystemStats();
      m_directories.emplace_back("/");
      m_rootPath = "./ps4_root";
      std::filesystem::create_directories(m_rootPath);

      // Mount root directory to /app0
      m_mountPoints.Mount(std::filesystem::path(m_rootPath), "/app0", false,
                          false);

      // Mount PKG installation directory
      std::filesystem::path pkgInstallPath =
          std::filesystem::path(m_rootPath) / "installed_packages";
      std::filesystem::create_directories(pkgInstallPath);
      m_mountPoints.Mount(pkgInstallPath, "/mnt/sandbox/pfsmnt", false, false);

      // Mount other standard directories
      m_mountPoints.Mount(std::filesystem::path(m_rootPath) / "dev", "/dev",
                          false, false);
      m_mountPoints.Mount(std::filesystem::path(m_rootPath) / "savedata",
                          m_settings.saveDataPath, false, false);
      m_mountPoints.Mount(std::filesystem::path(m_rootPath) / "trophy",
                          m_settings.trophyPath, false, false);
      m_mountPoints.Mount(std::filesystem::path(m_rootPath) / "system",
                          m_settings.systemPath, false, false);

      // Mount additional mounts from settings
      for (const auto &mount : m_settings.additionalMounts) {
        // Assuming additionalMounts are host paths and will be mounted to their
        // own name as guest path This might need adjustment based on actual
        // desired behavior
        m_mountPoints.Mount(std::filesystem::path(mount), mount, false, false);
      }

      // Check timeout before device files initialization
      auto current = std::chrono::steady_clock::now();
      if (current - start > overallTimeout) {
        spdlog::error("Filesystem initialization timed out before device files "
                      "initialization");
        return false;
      }

      if (m_settings.enableDeviceFiles) {
        spdlog::info("Initializing device files...");
        if (!InitializeAllDeviceFilesLocked()) {
          spdlog::error("Device files initialization failed");
          return false;
        }
        m_handleTable.CreateStdHandles();
        spdlog::info("Device files initialized");
      }

      // Check timeout before PFS initialization
      current = std::chrono::steady_clock::now();
      if (current - start > overallTimeout) {
        spdlog::error(
            "Filesystem initialization timed out before PFS initialization");
        return false;
      }

      if (m_settings.enablePFS) {
        spdlog::info("Initializing PFS...");
        if (!InitializePFSInternal()) {
          spdlog::error("PFS initialization failed");
          return false;
        }
        spdlog::info("PFS initialized");
      }

      // Update metrics and stats while still holding lock
      auto end = std::chrono::steady_clock::now();
      auto totalDuration =
          std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("PS4Filesystem initialized with root path: {} (took {}ms)",
                   m_rootPath, totalDuration.count());

    } catch (const std::exception &e) {
      spdlog::error("PS4Filesystem initialization failed: {}", e.what());
      m_stats.cacheMisses++;
      return false;
    }
    // Filesystem lock is automatically released here when the scope ends
  }

  // Update diagnostics after releasing filesystem lock to avoid lock ordering
  // violations The memory system (MMU) has lower lock level (3000) than
  // filesystem (7000)
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  return true;
}

// Shutdown the filesystem
void PS4Filesystem::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  try {
    COMPONENT_LOCK(m_mutex, "PS4Filesystem");
    for (auto &[fd, handle] : m_fileHandles) {
      if (handle.hostFd >= 3)
#ifdef _WIN32
        ::_close(handle.hostFd);
#else
        ::close(handle.hostFd);
#endif
    }
    m_files.clear();
    m_fileHandles.clear();
    m_directories.clear();
    m_mountPoints.UnmountAll(); // Use the MountPointManager's UnmountAll
    m_stats = FilesystemStats();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem shutdown");
    // COMPONENT_LOCK scope ends here
  } catch (const std::exception &e) {
    spdlog::error("PS4Filesystem shutdown failed: {}", e.what());
    m_stats.cacheMisses++;
  }

  // Update diagnostics after releasing lock to avoid potential deadlocks
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
}

// Open a file
int PS4Filesystem::OpenFile(const std::string &path, int flags, mode_t mode) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "PS4Filesystem");
  try {
    if (path.empty() || path[0] != '/') {
      spdlog::error("OpenFile: Invalid path: {}", path);
      errno = EINVAL;
      return -1;
    }

    bool is_read_only;
    std::filesystem::path hostPath =
        m_mountPoints.GetHostPath(path, &is_read_only);
    if (hostPath.empty()) {
      spdlog::error("OpenFile: Failed to map path: {}", path);
      errno = ENOENT;
      return -1;
    }
    if (is_read_only && (flags & (O_WRONLY | O_RDWR))) {
      spdlog::error("OpenFile: Write access denied for read-only path: {}",
                    path);
      errno = EACCES;
      return -1;
    }

    if (flags & O_CREAT) {
      std::filesystem::create_directories(hostPath.parent_path());
    }

    int fd = m_handleTable.CreateHandle();
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file) {
      spdlog::error("OpenFile: Failed to create handle for: {}", path);
      errno = EMFILE;
      return -1;
    }

#ifdef _WIN32
    int hostFlags = (flags & O_ACCMODE) == O_RDONLY   ? _O_RDONLY
                    : (flags & O_ACCMODE) == O_WRONLY ? _O_WRONLY
                                                      : _O_RDWR;
    if (flags & O_APPEND)
      hostFlags |= _O_APPEND;
    if (flags & O_CREAT)
      hostFlags |= _O_CREAT;
    if (flags & O_TRUNC)
      hostFlags |= _O_TRUNC;
    if (flags & O_EXCL)
      hostFlags |= _O_EXCL;
    hostFlags |= _O_BINARY;
    int pmode = _S_IREAD | (mode & S_IWUSR ? _S_IWRITE : 0);
    errno_t err = ::_sopen_s(&file->host_fd, hostPath.string().c_str(), hostFlags, _SH_DENYNO, pmode);
    if (err != 0) {
      file->host_fd = -1;
    }
#else
    file->host_fd = ::open(hostPath.c_str(), flags, mode);
#endif

    if (file->host_fd < 0) {
      m_handleTable.DeleteHandle(fd);
      spdlog::error("OpenFile: Failed to open {}: errno={}", hostPath.string(),
                    errno);
      m_stats.cacheMisses++;
      return -1;
    }

    file->is_opened = true;
    file->host_name = hostPath.string();
    file->guest_name = path;
    file->type = DetermineFileType(path);

    FileEntry &entry = m_files[path];
    entry.path = path;
    entry.hostPath = hostPath.string();
    struct stat st;
    if (::stat(hostPath.string().c_str(), &st) == 0) {
      entry.size = st.st_size;
      entry.mode = st.st_mode;
      entry.creationTime = st.st_ctime;
      entry.modificationTime = st.st_mtime;
      entry.accessTime = st.st_atime;
      entry.isDir = S_ISDIR(st.st_mode);
    } else {
      entry.size = 0;
      entry.mode = mode;
      entry.creationTime = std::time(nullptr);
      entry.modificationTime = entry.creationTime;
      entry.accessTime = entry.creationTime;
    }
    entry.fileType = file->type;

    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("OpenFile: Opened {} as FD {}, hostPath={}", path, fd,
                 hostPath.string());
    return fd;
  } catch (const std::exception &e) {
    spdlog::error("OpenFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

// Close a file
int PS4Filesystem::CloseFile(int fd) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file || !file->is_opened) {
      spdlog::error("CloseFile: Invalid FD: {}", fd);
      errno = EBADF;
      return -1;
    }
    if (file->host_fd >= 3) {
#ifdef _WIN32
      ::_close(file->host_fd);
#else
      ::close(file->host_fd);
#endif
    }
    m_handleTable.DeleteHandle(fd);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("CloseFile: Closed FD {}", fd);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("CloseFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

// Read from a file
ssize_t PS4Filesystem::ReadFile(int fd, void *buf, size_t count) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file || !file->is_opened) {
      spdlog::error("ReadFile: Invalid FD: {}", fd);
      errno = EBADF;
      return -1;
    }

    auto fileIt = m_files.find(file->guest_name);
    if (fileIt != m_files.end() && !fileIt->second.data.empty()) {
      size_t bytesToRead =
          std::min(count, fileIt->second.data.size() - fileIt->second.size);
      std::memcpy(buf, fileIt->second.data.data() + fileIt->second.size,
                  bytesToRead);
      fileIt->second.size += bytesToRead;
      fileIt->second.accessTime = std::time(nullptr);
      fileIt->second.cacheHits++;
      m_stats.cacheHits++;
      m_stats.operationCount++;
      m_stats.fileAccessCount++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("ReadFile: Read {} bytes from FD {} (cached)", bytesToRead,
                    fd);
      return static_cast<ssize_t>(bytesToRead);
    }

    lock.unlock();
    if (file->type == PS4FileType::Device) {
      if (HandleDeviceAccess(file->guest_name, buf, count, false)) {
        m_stats.cacheHits++;
        return static_cast<ssize_t>(count);
      }
      errno = EIO;
      return -1;
    }

    ssize_t bytesRead =
#ifdef _WIN32
        ::_read(file->host_fd, buf, static_cast<unsigned int>(count));
#else
        ::read(file->host_fd, buf, count);
#endif
    lock.lock();
    if (bytesRead < 0) {
      spdlog::error("ReadFile: Failed for FD {}: errno={}", fd, errno);
      m_stats.cacheMisses++;
      return -1;
    }

    if (fileIt != m_files.end()) {
      fileIt->second.accessTime = std::time(nullptr);
      if (fileIt->second.data.empty() && bytesRead > 0) {
        fileIt->second.data.resize(bytesRead);
        std::memcpy(fileIt->second.data.data(), buf, bytesRead);
      }
      fileIt->second.size += bytesRead;
    }
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("ReadFile: Read {} bytes from FD {}", bytesRead, fd);
    return bytesRead;
  } catch (const std::exception &e) {
    spdlog::error("ReadFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

// Write to a file
ssize_t PS4Filesystem::WriteFile(int fd, const void *buf, size_t count) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file || !file->is_opened) {
      spdlog::error("WriteFile: Invalid FD: {}", fd);
      errno = EBADF;
      return -1;
    }

    auto fileIt = m_files.find(file->guest_name);
    lock.unlock();
    if (file->type == PS4FileType::Device) {
      if (HandleDeviceAccess(file->guest_name, const_cast<void *>(buf), count,
                             true)) {
        m_stats.cacheHits++;
        return static_cast<ssize_t>(count);
      }
      errno = EIO;
      return -1;
    }

    ssize_t bytesWritten =
#ifdef _WIN32
        ::_write(file->host_fd, buf, static_cast<unsigned int>(count));
#else
        ::write(file->host_fd, buf, count);
#endif
    lock.lock();
    if (bytesWritten < 0) {
      spdlog::error("WriteFile: Failed for FD {}: errno={}", fd, errno);
      m_stats.cacheMisses++;
      return -1;
    }

    if (fileIt != m_files.end()) {
      fileIt->second.size =
          std::max(fileIt->second.size, fileIt->second.size + bytesWritten);
      fileIt->second.modificationTime = std::time(nullptr);
      if (!fileIt->second.data.empty()) {
        fileIt->second.data.resize(fileIt->second.size);
        std::memcpy(fileIt->second.data.data() +
                        (fileIt->second.size - bytesWritten),
                    buf, bytesWritten);
        fileIt->second.cacheHits++;
      }
    }
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("WriteFile: Wrote {} bytes to FD {}", bytesWritten, fd);
    return bytesWritten;
  } catch (const std::exception &e) {
    spdlog::error("WriteFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

// Seek in a file
off_t PS4Filesystem::SeekFile(int fd, off_t offset, int whence) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    HandleTable::File *file = m_handleTable.GetFile(fd);
    if (!file || !file->is_opened) {
      spdlog::error("SeekFile: Invalid FD: {}", fd);
      errno = EBADF;
      return -1;
    }

    lock.unlock();
    off_t newOffset =
#ifdef _WIN32
        ::_lseek(file->host_fd, static_cast<long>(offset), whence);
#else
        ::lseek(file->host_fd, offset, whence);
#endif
    lock.lock();
    if (newOffset < 0) {
      spdlog::error("SeekFile: Failed for FD {}: errno={}", fd, errno);
      m_stats.cacheMisses++;
      return -1;
    }

    auto fileIt = m_files.find(file->guest_name);
    if (fileIt != m_files.end()) {
      fileIt->second.size = newOffset;
    }
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SeekFile: FD {}, new offset={}", fd, newOffset);
    return newOffset;
  } catch (const std::exception &e) {
    spdlog::error("SeekFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

// Stat a file
#ifdef _WIN32
int PS4Filesystem::StatFile(
    const std::string &path,
    struct _stat64i32 *buf) { // Corrected type for Windows
#else
int PS4Filesystem::StatFile(const std::string &path, struct stat *buf) {
#endif
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
  try {
    auto it = m_files.find(path);
    if (it != m_files.end()) {
      buf->st_size = it->second.size;
      buf->st_mode = it->second.mode;
      buf->st_ctime = it->second.creationTime;
      buf->st_mtime = it->second.modificationTime;
      buf->st_atime = it->second.accessTime;
      it->second.cacheHits++;
      m_stats.cacheHits++;
      m_stats.operationCount++;
      m_stats.fileAccessCount++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("StatFile: Path {} (cached), size={}", path,
                    it->second.size);
      return 0;
    }

    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("StatFile: Failed to map path: {}", path);
      errno = ENOENT;
      return -1;
    }

    lock.unlock();
    if (::stat(hostPath.string().c_str(), buf) < 0) {
      spdlog::error("StatFile: Path not found: {}, errno={}", hostPath.string(),
                    errno);
      m_stats.cacheMisses++;
      return -1;
    }
    lock.lock();

    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("StatFile: Path {}, size={}", hostPath.string(),
                  buf->st_size);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("StatFile failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return -1;
  }
}

// Create a directory
bool PS4Filesystem::CreateDirectory(const std::string &path, mode_t mode) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("CreateDirectory: Failed to map path: {}", path);
      errno = ENOENT;
      return false;
    }

    lock.unlock();
    std::filesystem::create_directories(hostPath);
    lock.lock();

    struct stat st;
    if (::stat(hostPath.string().c_str(), &st) == 0) {
      m_directories.push_back(path);
      FileEntry &entry = m_files[path];
      entry.path = path;
      entry.hostPath = hostPath.string();
      entry.size = 0;
      entry.mode = mode ? mode : m_settings.defaultDirMode;
      entry.creationTime = st.st_ctime;
      entry.modificationTime = st.st_mtime;
      entry.accessTime = st.st_atime;
      entry.isDir = true;
      entry.fileType = PS4FileType::Directory;
      entry.cacheHits++;
    }
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CreateDirectory: path={}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateDirectory failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return false;
  }
}

// Remove a directory
bool PS4Filesystem::RemoveDirectory(const std::string &path) {
  try {
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("RemoveDirectory: Failed to map path: {}", path);
      errno = ENOENT;
      return false;
    }

    if (!std::filesystem::exists(hostPath)) {
      spdlog::warn("Directory does not exist: {}", path);
      return true;
    }
    if (!std::filesystem::is_directory(hostPath)) {
      spdlog::error("Path is not a directory: {}", path);
      errno = ENOTDIR;
      return false;
    }

    std::filesystem::remove_all(hostPath);
    COMPONENT_LOCK(m_mutex, "FilesystemMutex");
    m_directories.erase(
        std::remove(m_directories.begin(), m_directories.end(), path),
        m_directories.end());
    m_files.erase(path);
    spdlog::info("Removed directory: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("RemoveDirectory failed for {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

// Mount a directory
bool PS4Filesystem::MountDirectory(const std::wstring &path) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::filesystem::path hostPath(path);
    std::string guestPath = "/mnt/" + hostPath.filename().string();
    m_mountPoints.Mount(hostPath, guestPath, false, false);
    m_directories.push_back(guestPath);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("MountDirectory: path={}", guestPath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("MountDirectory failed: {}", e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return false;
  }
}

// CORRECTED: Allocate virtual memory
uint64_t PS4Filesystem::AllocateVirtualMemory(uint64_t size, uint64_t alignment,
                                              bool shared) {
  auto start = std::chrono::steady_clock::now();
  uint64_t addr = 0;

  try {
    // Call memory allocation without holding the filesystem mutex to avoid lock
    // ordering violation. The memory system (MMU/OrbisOS) has a lower lock
    // level (3000) than the filesystem (7000).
    addr =
        m_emulator.GetOrbisOS().AllocateVirtualMemory(size, alignment, shared);

    // Now, acquire the filesystem mutex only to update its internal statistics.
    {
      COMPONENT_LOCK(m_mutex, "PS4Filesystem");
      m_stats.operationCount++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
    }
    return addr;
  } catch (const std::exception &e) {
    spdlog::error("AllocateVirtualMemory failed: {}", e.what());
    {
      COMPONENT_LOCK(m_mutex, "PS4Filesystem");
      m_stats.cacheMisses++;
    }
    return 0;
  }
}

// CORRECTED: Free virtual memory
bool PS4Filesystem::FreeVirtualMemory(uint64_t address) {
  auto start = std::chrono::steady_clock::now();

  try {
    // Call memory deallocation without holding the filesystem mutex.
    m_emulator.GetOrbisOS().FreeVirtualMemory(address);

    // Acquire the lock only to update stats.
    {
      COMPONENT_LOCK(m_mutex, "PS4Filesystem");
      m_stats.operationCount++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("FreeVirtualMemory failed: {}", e.what());
    {
      COMPONENT_LOCK(m_mutex, "PS4Filesystem");
      m_stats.cacheMisses++;
    }
    return false;
  }
}

// CORRECTED: Protect memory
bool PS4Filesystem::ProtectMemory(uint64_t address, uint64_t size,
                                  int protection) {
  auto start = std::chrono::steady_clock::now();
  bool success = false;

  try {
    // Call memory protection without holding the filesystem mutex.
    success = m_emulator.GetOrbisOS().ProtectMemory(address, size, protection);

    // Acquire the lock only to update stats.
    {
      COMPONENT_LOCK(m_mutex, "PS4Filesystem");
      m_stats.operationCount++;
      if (success) {
        m_stats.cacheHits++;
      } else {
        m_stats.cacheMisses++;
      }
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
    }
    return success;
  } catch (const std::exception &e) {
    spdlog::error("ProtectMemory failed: {}", e.what());
    {
      COMPONENT_LOCK(m_mutex, "PS4Filesystem");
      m_stats.cacheMisses++;
    }
    return false;
  }
}

// CORRECTED: Get process ID
uint64_t PS4Filesystem::SceKernelGetProcessId() {
  auto start = std::chrono::steady_clock::now();
  uint64_t pid = 1; // Default fallback

  try {
    // Call OS function without holding the filesystem mutex.
    pid = m_emulator.GetOrbisOS().SceKernelGetProcessId();

    // Acquire the lock only to update stats.
    {
      COMPONENT_LOCK(m_mutex, "PS4Filesystem");
      m_stats.operationCount++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
    }
    return pid;
  } catch (const std::exception &e) {
    spdlog::error("SceKernelGetProcessId failed: {}", e.what());
    {
      COMPONENT_LOCK(m_mutex, "PS4Filesystem");
      m_stats.cacheMisses++;
    }
    return 1; // Return a default PID on error
  }
}

// Dump filesystem state
std::string PS4Filesystem::DumpState() const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::ostringstream oss;
    oss << "PS4 Filesystem State Dump\n";
    oss << "Root Path: " << m_rootPath << "\n";
    oss << "Open File Handles: " << m_fileHandles.size() << "\n";
    oss << "Total Files: " << m_files.size() << "\n";
    oss << "Directories: " << m_directories.size() << "\n";
    oss << "Device Files: " << m_deviceFiles.size() << "\n";
    oss << "Stats: Ops=" << m_stats.operationCount
        << ", Latency=" << m_stats.totalLatencyUs << "us, "
        << "Hits=" << m_stats.cacheHits << ", Misses=" << m_stats.cacheMisses
        << ", Accesses=" << m_stats.fileAccessCount << "\n";
    oss << "Game Loaded: " << (m_gameLoaded ? "Yes" : "No");
    if (m_gameLoaded)
      oss << " (" << m_loadedGamePath << ")";
    oss << "\n";
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return oss.str();
  } catch (const std::exception &e) {
    spdlog::error("DumpState failed: {}", e.what());
    return "Error dumping state: " + std::string(e.what());
  }
}

// Save filesystem state
void PS4Filesystem::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
  try {
    uint64_t fileCount = m_files.size();
    out.write(reinterpret_cast<const char *>(&fileCount), sizeof(fileCount));
    for (const auto &[path, entry] : m_files) {
      uint64_t pathLen = path.size();
      out.write(reinterpret_cast<const char *>(&pathLen), sizeof(pathLen));
      out.write(path.c_str(), pathLen);
      uint64_t hostPathLen = entry.hostPath.size();
      out.write(reinterpret_cast<const char *>(&hostPathLen),
                sizeof(hostPathLen));
      out.write(entry.hostPath.c_str(), hostPathLen);
      out.write(reinterpret_cast<const char *>(&entry.size),
                sizeof(entry.size));
      out.write(reinterpret_cast<const char *>(&entry.protection),
                sizeof(entry.protection));
      out.write(reinterpret_cast<const char *>(&entry.mode),
                sizeof(entry.mode));
      out.write(reinterpret_cast<const char *>(&entry.creationTime),
                sizeof(entry.creationTime));
      out.write(reinterpret_cast<const char *>(&entry.modificationTime),
                sizeof(entry.modificationTime));
      out.write(reinterpret_cast<const char *>(&entry.accessTime),
                sizeof(entry.accessTime));
      out.write(reinterpret_cast<const char *>(&entry.isDir),
                sizeof(entry.isDir));
      uint64_t dataSize = entry.data.size();
      out.write(reinterpret_cast<const char *>(&dataSize), sizeof(dataSize));
      if (dataSize > 0) {
        out.write(reinterpret_cast<const char *>(entry.data.data()), dataSize);
      }
      out.write(reinterpret_cast<const char *>(&entry.fileType),
                sizeof(entry.fileType));
      out.write(reinterpret_cast<const char *>(&entry.ps4Permissions),
                sizeof(entry.ps4Permissions));
      uint64_t mountPointLen = entry.mountPoint.size();
      out.write(reinterpret_cast<const char *>(&mountPointLen),
                sizeof(mountPointLen));
      out.write(entry.mountPoint.c_str(), mountPointLen);
      out.write(reinterpret_cast<const char *>(&entry.isEncrypted),
                sizeof(entry.isEncrypted));
      out.write(reinterpret_cast<const char *>(&entry.blockSize),
                sizeof(entry.blockSize));
      uint64_t checksumSize = entry.checksum.size();
      out.write(reinterpret_cast<const char *>(&checksumSize),
                sizeof(checksumSize));
      if (checksumSize > 0) {
        out.write(reinterpret_cast<const char *>(entry.checksum.data()),
                  checksumSize);
      }
      out.write(reinterpret_cast<const char *>(&entry.present),
                sizeof(entry.present));
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }
    uint64_t handleCount = m_fileHandles.size();
    out.write(reinterpret_cast<const char *>(&handleCount),
              sizeof(handleCount));
    for (const auto &[fd, handle] : m_fileHandles) {
      out.write(reinterpret_cast<const char *>(&fd), sizeof(fd));
      uint64_t pathLen = handle.path.size();
      out.write(reinterpret_cast<const char *>(&pathLen), sizeof(pathLen));
      out.write(handle.path.c_str(), pathLen);
      out.write(reinterpret_cast<const char *>(&handle.flags),
                sizeof(handle.flags));
      out.write(reinterpret_cast<const char *>(&handle.offset),
                sizeof(handle.offset));
      out.write(reinterpret_cast<const char *>(&handle.hostFd),
                sizeof(handle.hostFd));
      out.write(reinterpret_cast<const char *>(&handle.fd), sizeof(handle.fd));
      out.write(reinterpret_cast<const char *>(&handle.cacheHits),
                sizeof(handle.cacheHits));
      out.write(reinterpret_cast<const char *>(&handle.cacheMisses),
                sizeof(handle.cacheMisses));
    }
    uint64_t dirCount = m_directories.size();
    out.write(reinterpret_cast<const char *>(&dirCount), sizeof(dirCount));
    for (const auto &dir : m_directories) {
      uint64_t dirLen = dir.size();
      out.write(reinterpret_cast<const char *>(&dirLen), sizeof(dirLen));
      out.write(dir.c_str(), dirLen);
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem state saved");
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
  }
}

// Load filesystem state
void PS4Filesystem::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    m_files.clear();
    m_fileHandles.clear();
    m_directories.clear();
    uint64_t fileCount;
    in.read(reinterpret_cast<char *>(&fileCount), sizeof(fileCount));
    for (uint64_t i = 0; i < fileCount; ++i) {
      uint64_t pathLen;
      in.read(reinterpret_cast<char *>(&pathLen), sizeof(pathLen));
      std::string path(pathLen, '\0');
      in.read(&path[0], pathLen);
      FileEntry entry;
      entry.path = path;
      uint64_t hostPathLen;
      in.read(reinterpret_cast<char *>(&hostPathLen), sizeof(hostPathLen));
      entry.hostPath.resize(hostPathLen);
      in.read(&entry.hostPath[0], hostPathLen);
      in.read(reinterpret_cast<char *>(&entry.size), sizeof(entry.size));
      in.read(reinterpret_cast<char *>(&entry.protection),
              sizeof(entry.protection));
      in.read(reinterpret_cast<char *>(&entry.mode), sizeof(entry.mode));
      in.read(reinterpret_cast<char *>(&entry.creationTime),
              sizeof(entry.creationTime));
      in.read(reinterpret_cast<char *>(&entry.modificationTime),
              sizeof(entry.modificationTime));
      in.read(reinterpret_cast<char *>(&entry.accessTime),
              sizeof(entry.accessTime));
      in.read(reinterpret_cast<char *>(&entry.isDir), sizeof(entry.isDir));
      uint64_t dataSize;
      in.read(reinterpret_cast<char *>(&dataSize), sizeof(dataSize));
      entry.data.resize(dataSize);
      if (dataSize > 0) {
        in.read(reinterpret_cast<char *>(entry.data.data()), dataSize);
      }
      in.read(reinterpret_cast<char *>(&entry.fileType),
              sizeof(entry.fileType));
      in.read(reinterpret_cast<char *>(&entry.ps4Permissions),
              sizeof(entry.ps4Permissions));
      uint64_t mountPointLen;
      in.read(reinterpret_cast<char *>(&mountPointLen), sizeof(mountPointLen));
      entry.mountPoint.resize(mountPointLen);
      in.read(&entry.mountPoint[0], mountPointLen);
      in.read(reinterpret_cast<char *>(&entry.isEncrypted),
              sizeof(entry.isEncrypted));
      in.read(reinterpret_cast<char *>(&entry.blockSize),
              sizeof(entry.blockSize));
      uint64_t checksumSize;
      in.read(reinterpret_cast<char *>(&checksumSize), sizeof(checksumSize));
      entry.checksum.resize(checksumSize);
      if (checksumSize > 0) {
        in.read(reinterpret_cast<char *>(entry.checksum.data()), checksumSize);
      }
      in.read(reinterpret_cast<char *>(&entry.present), sizeof(entry.present));
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      m_files[path] = entry;
    }
    uint64_t handleCount;
    in.read(reinterpret_cast<char *>(&handleCount), sizeof(handleCount));
    for (uint64_t i = 0; i < handleCount; ++i) {
      int fd;
      in.read(reinterpret_cast<char *>(&fd), sizeof(fd));
      FileHandle handle;
      uint64_t pathLen;
      in.read(reinterpret_cast<char *>(&pathLen), sizeof(pathLen));
      handle.path.resize(pathLen);
      in.read(&handle.path[0], pathLen);
      in.read(reinterpret_cast<char *>(&handle.flags), sizeof(handle.flags));
      in.read(reinterpret_cast<char *>(&handle.offset), sizeof(handle.offset));
      in.read(reinterpret_cast<char *>(&handle.hostFd), sizeof(handle.hostFd));
      in.read(reinterpret_cast<char *>(&handle.fd), sizeof(handle.fd));
      in.read(reinterpret_cast<char *>(&handle.cacheHits),
              sizeof(handle.cacheHits));
      in.read(reinterpret_cast<char *>(&handle.cacheMisses),
              sizeof(handle.cacheMisses));
      m_fileHandles[fd] = handle;
      m_nextFd = std::max(m_nextFd, fd + 1);
    }
    uint64_t dirCount;
    in.read(reinterpret_cast<char *>(&dirCount), sizeof(dirCount));
    for (uint64_t i = 0; i < dirCount; ++i) {
      uint64_t dirLen;
      in.read(reinterpret_cast<char *>(&dirLen), sizeof(dirLen));
      std::string dir(dirLen, '\0');
      in.read(&dir[0], dirLen);
      m_directories.push_back(dir);
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem state loaded");
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
  }
}

// Get filesystem stats
FilesystemStats PS4Filesystem::GetStats() const {
  COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
  try {
    return m_stats;
  } catch (const std::exception &e) {
    spdlog::error("GetStats failed: {}", e.what());
    return m_stats;
  }
}

// Set filesystem settings
void PS4Filesystem::SetSettings(const EnhancedSettings &settings) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    m_settings = settings;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SetSettings: Updated settings");
  } catch (const std::exception &e) {
    spdlog::error("SetSettings failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

// Get filesystem settings
const EnhancedSettings &PS4Filesystem::GetSettings() const {
  COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
  try {
    return m_settings;
  } catch (const std::exception &e) {
    spdlog::error("GetSettings failed: {}", e.what());
    return m_settings;
  }
}

// Save settings to file
bool PS4Filesystem::SaveSettings(const std::string &filename) const {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::ofstream out(filename);
    if (!out) {
      spdlog::error("SaveSettings: Cannot open file: {}", filename);
      errno = EIO;
      return false;
    }
    out << "version=1\n";
    out << "saveDataPath=" << m_settings.saveDataPath << "\n";
    out << "trophyPath=" << m_settings.trophyPath << "\n";
    out << "systemPath=" << m_settings.systemPath << "\n";
    out << "defaultMountPoint=" << m_settings.defaultMountPoint << "\n";
    out << "enablePFS=" << m_settings.enablePFS << "\n";
    out << "enableDeviceFiles=" << m_settings.enableDeviceFiles << "\n";
    out << "enableCaseSensitivity=" << m_settings.enableCaseSensitivity << "\n";
    out << "defaultFileMode=" << m_settings.defaultFileMode << "\n";
    out << "defaultDirMode=" << m_settings.defaultDirMode << "\n";
    out << "pfsBlockSize=" << m_settings.pfsBlockSize << "\n";
    out << "cacheSize=" << m_settings.cacheSize << "\n";
    out << "additionalMounts=";
    for (const auto &mount : m_settings.additionalMounts) {
      out << mount << ";";
    }
    out << "\n";
    out.close();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem settings saved to: {}", filename);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SaveSettings failed: {}", e.what());
    errno = EIO;
    return false;
  }
}

// Load settings from file
bool PS4Filesystem::LoadSettings(const std::string &filename) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::ifstream in(filename);
    if (!in) {
      spdlog::error("LoadSettings: Cannot open file: {}", filename);
      errno = ENOENT;
      return false;
    }
    std::string line;
    while (std::getline(in, line)) {
      auto pos = line.find('=');
      if (pos == std::string::npos)
        continue;
      auto key = line.substr(0, pos);
      auto value = line.substr(pos + 1);
      if (key == "version") {
        // Handle version if needed
      } else if (key == "saveDataPath") {
        m_settings.saveDataPath = value;
      } else if (key == "trophyPath") {
        m_settings.trophyPath = value;
      } else if (key == "systemPath") {
        m_settings.systemPath = value;
      } else if (key == "defaultMountPoint") {
        m_settings.defaultMountPoint = value;
      } else if (key == "enablePFS") {
        m_settings.enablePFS = (value == "1");
      } else if (key == "enableDeviceFiles") {
        m_settings.enableDeviceFiles = (value == "1");
      } else if (key == "enableCaseSensitivity") {
        m_settings.enableCaseSensitivity = (value == "1");
      } else if (key == "defaultFileMode") {
        m_settings.defaultFileMode = std::stoi(value);
      } else if (key == "defaultDirMode") {
        m_settings.defaultDirMode = std::stoi(value);
      } else if (key == "pfsBlockSize") {
        m_settings.pfsBlockSize = std::stoi(value);
      } else if (key == "cacheSize") {
        m_settings.cacheSize = std::stoi(value);
      } else if (key == "additionalMounts") {
        std::istringstream iss(value);
        std::string mount;
        while (std::getline(iss, mount, ';')) {
          if (!mount.empty())
            m_settings.additionalMounts.push_back(mount);
        }
      }
    }
    in.close();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem settings loaded from: {}", filename);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("LoadSettings failed: {}", e.what());
    errno = EIO;
    return false;
  }
}

// Initialize PFS (Protected File System)
bool PS4Filesystem::InitializePFS() {
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  return InitializePFSInternal();
}

// Internal PFS initialization
bool PS4Filesystem::InitializePFSInternal() {
  auto start = std::chrono::steady_clock::now();
  const auto timeout = std::chrono::seconds(60);

  try {
    spdlog::info("Initializing Protected File System (PFS)...");

    auto checkTimeout = [&]() {
      auto current = std::chrono::steady_clock::now();
      if (current - start > timeout) {
        spdlog::error("PFS initialization timed out");
        return true;
      }
      return false;
    };

    if (checkTimeout())
      return false;

    if (m_pfsKey.empty()) {
      if (!GeneratePFSKey(m_pfsKey, "ps4_default_pfs_key")) {
        spdlog::error("Failed to generate default PFS key");
        return false;
      }
    }

    if (checkTimeout())
      return false;

    std::vector<std::string> pfsDirs = {"/pfs", "/mnt/pfs",
                                        "/mnt/sandbox/pfsmnt"};
    for (const auto &dir : pfsDirs) {
      FileEntry &entry = m_files[dir];
      entry.path = dir;
      entry.hostPath = m_mountPoints.GetHostPath(dir).string();
      if (entry.hostPath.empty()) {
        entry.hostPath = ResolvePath(dir);
      }
      entry.fileType = PS4FileType::Directory;
      entry.size = 0;
      entry.mode = 0755;
      entry.modificationTime = std::time(nullptr);
      entry.present = true;
      entry.isDir = true;

      if (std::find(m_directories.begin(), m_directories.end(), dir) ==
          m_directories.end()) {
        m_directories.push_back(dir);
      }

      try {
        std::filesystem::create_directories(entry.hostPath);
        spdlog::debug("Created PFS directory: {}", entry.hostPath);
      } catch (const std::exception &e) {
        spdlog::warn("Error creating PFS directory {}: {}", entry.hostPath,
                     e.what());
      }
    }

    // Create the base PFS directory on the host
    std::filesystem::path pfsHostBasePath =
        std::filesystem::path(m_rootPath) / "pfs";
    std::filesystem::create_directories(pfsHostBasePath);

    std::vector<std::string> standardPfsContainers = {
        "app.pfs", "patch.pfs", "system.pfs", "addcont.pfs"};
    for (const auto &container : standardPfsContainers) {
      if (checkTimeout())
        return false;

      // Construct the full HOST path to the PFS file
      std::string hostPath = (pfsHostBasePath / container).string();

      // Construct the VIRTUAL mount point
      std::string mountpoint =
          "/mnt/pfs/" + container.substr(0, container.find('.'));

      // Create the PFS container file on the host if it doesn't exist
      if (!std::filesystem::exists(hostPath) && m_settings.enablePFS) {
        spdlog::info("Creating standard PFS container: {}", hostPath);
        std::ofstream pfsFile(hostPath, std::ios::binary);
        if (pfsFile.is_open()) {
          std::vector<uint8_t> header(256, 0);
          header[0] = 'P';
          header[1] = 'F';
          header[2] = 'S';
          header[3] = 0;
          header[4] = 1;
          header[5] = 0;
          header[6] = 1;
          *reinterpret_cast<uint32_t *>(&header[8]) = 4096;
          pfsFile.write(reinterpret_cast<const char *>(header.data()),
                        header.size());
          pfsFile.close();
        }
      }

      // Mount the PFS file
      if (std::filesystem::exists(hostPath) && m_settings.enablePFS) {
        // *** FIX: Pass the correct host path and virtual mount point ***
        if (!MountPFSLocked(hostPath, mountpoint, m_pfsKey)) {
          // *** FIX: Propagate the failure ***
          spdlog::error("Failed to mount PFS container: {}", hostPath);
          return false;
        }
      }
    }

    spdlog::info("PFS initialization completed successfully");
    return true;
  } catch (const std::exception &e) {
    spdlog::error("InitializePFS failed: {}", e.what());
    return false;
  }
}

// Create a PFS file
bool PS4Filesystem::CreatePFSFile(const std::string &path, uint64_t size,
                                  bool encrypted) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    if (path.empty() || size == 0) {
      spdlog::error("CreatePFSFile: Invalid parameters: path={}, size={}", path,
                    size);
      return false;
    }

    std::string hostPath = MapToHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("CreatePFSFile: Failed to map path: {}", path);
      return false;
    }

    std::filesystem::create_directories(
        std::filesystem::path(hostPath).parent_path());

    PFSFile pfsFile;
    pfsFile.path = path;
    pfsFile.hostPath = hostPath;
    pfsFile.size = size;
    pfsFile.isEncrypted = encrypted;
    pfsFile.blockSize = m_settings.pfsBlockSize;
    pfsFile.blockCount = (size + pfsFile.blockSize - 1) / pfsFile.blockSize;

    if (encrypted) {
      std::vector<uint8_t> fileSpecificSeed(16);
      std::string seedStr =
          path +
          std::to_string(
              std::chrono::system_clock::now().time_since_epoch().count());
      std::hash<std::string> hasher;
      size_t seedHash = hasher(seedStr);
      *reinterpret_cast<size_t *>(fileSpecificSeed.data()) = seedHash;

      pfsFile.key.resize(32);
      for (size_t i = 0; i < pfsFile.key.size(); i++) {
        pfsFile.key[i] = m_pfsKey[i % m_pfsKey.size()] ^
                         fileSpecificSeed[i % fileSpecificSeed.size()];
      }

      pfsFile.iv.resize(16);
      for (size_t i = 0; i < pfsFile.iv.size(); i++) {
        pfsFile.iv[i] = fileSpecificSeed[i % fileSpecificSeed.size()];
      }
    }

    std::ofstream file(hostPath, std::ios::binary);
    if (!file) {
      spdlog::error("CreatePFSFile: Failed to create file: {}", hostPath);
      return false;
    }

    std::vector<uint8_t> header(256, 0);
    header[0] = 'P';
    header[1] = 'F';
    header[2] = 'S';
    header[3] = 0;
    header[4] = 1;
    header[5] = 0;
    header[6] = encrypted ? 1 : 0;
    *reinterpret_cast<uint32_t *>(&header[8]) =
        static_cast<uint32_t>(pfsFile.blockSize);
    *reinterpret_cast<uint64_t *>(&header[16]) = pfsFile.size;
    *reinterpret_cast<uint64_t *>(&header[24]) = pfsFile.blockCount;
    file.write(reinterpret_cast<const char *>(header.data()), header.size());

    std::vector<uint8_t> emptyBlock(pfsFile.blockSize, 0);
    for (uint64_t i = 0; i < pfsFile.blockCount; i++) {
      file.write(reinterpret_cast<const char *>(emptyBlock.data()),
                 emptyBlock.size());
    }

    file.close();

    m_pfsFiles[path] = pfsFile;

    FileEntry &entry = m_files[path];
    entry.path = path;
    entry.hostPath = hostPath;
    entry.fileType = PS4FileType::PFS;
    entry.size = size;
    entry.mode = 0644;
    entry.blockSize = pfsFile.blockSize;
    entry.isEncrypted = encrypted;
    entry.present = true;
    entry.modificationTime = std::time(nullptr);
    entry.creationTime = entry.modificationTime;
    entry.accessTime = entry.modificationTime;

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Created PFS file: {}, size={}, encrypted={}", path, size,
                 encrypted);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreatePFSFile failed: {}", e.what());
    return false;
  }
}

// Delete a PFS file
bool PS4Filesystem::DeletePFSFile(const std::string &path) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    auto it = m_pfsFiles.find(path);
    if (it == m_pfsFiles.end()) {
      spdlog::error("DeletePFSFile: File not found: {}", path);
      return false;
    }

    std::filesystem::remove(it->second.hostPath);
    m_pfsFiles.erase(it);
    m_files.erase(path);

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Deleted PFS file: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("DeletePFSFile failed: {}", e.what());
    return false;
  }
}

// Mount a PFS (assumes mutex is already held)
bool PS4Filesystem::MountPFSLocked(const std::string &pfspath,
                                   const std::string &mountpoint,
                                   const std::vector<uint8_t> &key) {
  auto start = std::chrono::steady_clock::now();
  try {
    if (m_pfsMounts.count(mountpoint)) {
      spdlog::error("MountPFSLocked: Mount point already in use: {}",
                    mountpoint);
      return false;
    }

    // *** FIX: The 'pfspath' is now the correct HOST path, no need to map it
    // again. ***
    if (!std::filesystem::exists(pfspath)) {
      spdlog::error("MountPFSLocked: PFS file not found: {}", pfspath);
      return false;
    }

    std::ifstream file(pfspath, std::ios::binary);
    if (!file) {
      spdlog::error("MountPFSLocked: Failed to open PFS file: {}", pfspath);
      return false;
    }

    std::vector<uint8_t> header(256);
    file.read(reinterpret_cast<char *>(header.data()), header.size());
    if (file.gcount() != header.size() || header[0] != 'P' ||
        header[1] != 'F' || header[2] != 'S') {
      spdlog::error("MountPFSLocked: Invalid PFS signature in file: {}",
                    pfspath);
      return false;
    }
    file.close();

    m_pfsMounts[mountpoint] = pfspath;
    m_mountPoints.Mount(std::filesystem::path(pfspath), mountpoint, false,
                        true);

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Mounted PFS: {} -> {}", pfspath, mountpoint);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("MountPFSLocked failed: {}", e.what());
    return false;
  }
}

// Mount a PFS
bool PS4Filesystem::MountPFS(const std::string &pfspath,
                             const std::string &mountpoint,
                             const std::vector<uint8_t> &key) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    if (m_pfsMounts.find(mountpoint) != m_pfsMounts.end()) {
      spdlog::error("MountPFS: Mount point already in use: {}", mountpoint);
      return false;
    }

    std::string hostPath = MapToHostPath(pfspath);
    if (!std::filesystem::exists(hostPath)) {
      spdlog::error("MountPFS: PFS file not found: {}", pfspath);
      return false;
    }

    std::ifstream file(hostPath, std::ios::binary);
    if (!file) {
      spdlog::error("MountPFS: Failed to open PFS file: {}", hostPath);
      return false;
    }

    std::vector<uint8_t> header(256);
    file.read(reinterpret_cast<char *>(header.data()), header.size());

    if (header[0] != 'P' || header[1] != 'F' || header[2] != 'S' ||
        header[3] != 0) {
      spdlog::error("MountPFS: Invalid PFS file format: {}", pfspath);
      return false;
    }

    m_pfsMounts[mountpoint] = pfspath;

    std::string mountHostPath = MapToHostPath(mountpoint);
    std::filesystem::create_directories(mountHostPath);

    FileEntry &entry = m_files[mountpoint];
    entry.path = mountpoint;
    entry.hostPath = mountHostPath;
    entry.fileType = PS4FileType::Directory;
    entry.isDir = true;
    entry.mode = 0755;
    entry.present = true;
    entry.modificationTime = std::time(nullptr);
    entry.creationTime = entry.modificationTime;
    entry.accessTime = entry.modificationTime;

    if (std::find(m_directories.begin(), m_directories.end(), mountpoint) ==
        m_directories.end()) {
      m_directories.push_back(mountpoint);
    }

    m_mountPoints.Mount(std::filesystem::path(hostPath), mountpoint, false,
                        true);

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Mounted PFS: {} -> {}", pfspath, mountpoint);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("MountPFS failed: {}", e.what());
    return false;
  }
}

// Unmount a PFS
bool PS4Filesystem::UnmountPFS(const std::string &mountpoint) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    auto it = m_pfsMounts.find(mountpoint);
    if (it == m_pfsMounts.end()) {
      spdlog::error("UnmountPFS: Mount point not found: {}", mountpoint);
      return false;
    }

    m_mountPoints.Unmount(mountpoint); // Use the MountPointManager's Unmount
    m_pfsMounts.erase(it);

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Unmounted PFS: {}", mountpoint);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("UnmountPFS failed: {}", e.what());
    return false;
  }
}

// Read from a PFS file
ssize_t PS4Filesystem::ReadPFSFile(const std::string &path, void *buf,
                                   uint64_t offset, size_t count) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    auto it = m_pfsFiles.find(path);
    if (it == m_pfsFiles.end()) {
      spdlog::error("ReadPFSFile: File not found: {}", path);
      return -1;
    }

    PFSFile &file = it->second;
    if (offset >= file.size)
      return 0;
    count = std::min(count, static_cast<size_t>(file.size - offset));
    if (count == 0)
      return 0;

    uint64_t startBlock = offset / file.blockSize;
    uint64_t endBlock = (offset + count - 1) / file.blockSize;
    uint64_t bytesRead = 0;
    uint8_t *bufPtr = static_cast<uint8_t *>(buf);

    for (uint64_t blockIndex = startBlock; blockIndex <= endBlock;
         blockIndex++) {
      PFSBlock *block = GetPFSBlock(file, blockIndex);
      if (!block) {
        spdlog::error("ReadPFSFile: Failed to read block {}: file={}",
                      blockIndex, path);
        if (bytesRead > 0)
          return bytesRead;
        return -1;
      }

      uint64_t blockOffset = blockIndex * file.blockSize;
      uint64_t blockStart = (offset > blockOffset) ? (offset - blockOffset) : 0;
      uint64_t bytesToRead =
          std::min(file.blockSize - blockStart, count - bytesRead);

      std::memcpy(bufPtr + bytesRead, block->data.data() + blockStart,
                  bytesToRead);
      bytesRead += bytesToRead;
    }

    file.accessTime = std::time(nullptr);
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return bytesRead;
  } catch (const std::exception &e) {
    spdlog::error("ReadPFSFile failed: {}", e.what());
    return -1;
  }
}

// Write to a PFS file
ssize_t PS4Filesystem::WritePFSFile(const std::string &path, const void *buf,
                                    uint64_t offset, size_t count) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    auto it = m_pfsFiles.find(path);
    if (it == m_pfsFiles.end()) {
      spdlog::error("WritePFSFile: File not found: {}", path);
      return -1;
    }

    PFSFile &file = it->second;
    if (offset + count > file.size) {
      file.size = offset + count;
      uint64_t newBlockCount =
          (file.size + file.blockSize - 1) / file.blockSize;
      if (newBlockCount > file.blockCount) {
        file.blockCount = newBlockCount;
        std::fstream fileStream(file.hostPath, std::ios::binary | std::ios::in |
                                                   std::ios::out);
        if (fileStream) {
          fileStream.seekp(16);
          fileStream.write(reinterpret_cast<const char *>(&file.size),
                           sizeof(file.size));
          fileStream.seekp(24);
          fileStream.write(reinterpret_cast<const char *>(&file.blockCount),
                           sizeof(file.blockCount));
          fileStream.close();
        }
      }
    }

    uint64_t startBlock = offset / file.blockSize;
    uint64_t endBlock = (offset + count - 1) / file.blockSize;
    uint64_t bytesWritten = 0;
    const uint8_t *bufPtr = static_cast<const uint8_t *>(buf);

    for (uint64_t blockIndex = startBlock; blockIndex <= endBlock;
         blockIndex++) {
      PFSBlock *block = GetPFSBlock(file, blockIndex, true);
      if (!block) {
        spdlog::error("WritePFSFile: Failed to get block {}: file={}",
                      blockIndex, path);
        if (bytesWritten > 0)
          return bytesWritten;
        return -1;
      }

      uint64_t blockOffset = blockIndex * file.blockSize;
      uint64_t blockStart = (offset > blockOffset) ? (offset - blockOffset) : 0;
      uint64_t bytesToWrite =
          std::min(file.blockSize - blockStart, count - bytesWritten);

      std::memcpy(block->data.data() + blockStart, bufPtr + bytesWritten,
                  bytesToWrite);
      block->dirty = true;
      bytesWritten += bytesToWrite;
    }

    file.modificationTime = std::time(nullptr);
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return bytesWritten;
  } catch (const std::exception &e) {
    spdlog::error("WritePFSFile failed: {}", e.what());
    return -1;
  }
}

// Validate PFS checksum
bool PS4Filesystem::ValidatePFSChecksum(const std::string &path) {
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    auto it = m_pfsFiles.find(path);
    if (it == m_pfsFiles.end()) {
      spdlog::error("ValidatePFSChecksum: File not found: {}", path);
      return false;
    }

    PFSFile &file = it->second;
    std::ifstream fileStream(file.hostPath, std::ios::binary);
    if (!fileStream) {
      spdlog::error("ValidatePFSChecksum: Failed to open file: {}",
                    file.hostPath);
      return false;
    }

    std::vector<uint8_t> header(256);
    fileStream.read(reinterpret_cast<char *>(header.data()), header.size());
    if (header[0] != 'P' || header[1] != 'F' || header[2] != 'S' ||
        header[3] != 0) {
      spdlog::error("ValidatePFSChecksum: Invalid PFS file format: {}", path);
      return false;
    }

    bool valid = true;
    for (uint64_t blockIndex = 0; blockIndex < file.blockCount; blockIndex++) {
      auto blockIt = file.blocks.find(blockIndex);
      if (blockIt != file.blocks.end() && !blockIt->second.checksum.empty()) {
        continue;
      }

      uint64_t blockOffset = 256 + (blockIndex * file.blockSize);
      std::vector<uint8_t> blockData(file.blockSize);
      fileStream.seekg(blockOffset);
      fileStream.read(reinterpret_cast<char *>(blockData.data()),
                      file.blockSize);
      if (!fileStream.good()) {
        spdlog::error("ValidatePFSChecksum: Failed to read block {}: file={}",
                      blockIndex, path);
        valid = false;
        break;
      }

      std::vector<uint8_t> checksum = CalculateChecksum(blockData);
      if (blockIt != file.blocks.end()) {
        blockIt->second.checksum = checksum;
      }
    }

    fileStream.close();
    return valid;
  } catch (const std::exception &e) {
    spdlog::error("ValidatePFSChecksum failed: {}", e.what());
    return false;
  }
}

// Initialize all device files (assumes mutex is already held)
bool PS4Filesystem::InitializeAllDeviceFilesLocked() {
  auto start = std::chrono::steady_clock::now();
  const auto timeout =
      std::chrono::seconds(90); // 90 second timeout for device initialization

  try {
    spdlog::info("Starting device file initialization...");

    // Mutex is already held by caller

    auto checkTimeout = [&]() {
      auto current = std::chrono::steady_clock::now();
      if (current - start > timeout) {
        spdlog::error("Device file initialization timed out after 90 seconds");
        return true;
      }
      return false;
    };

    // Create device files in batches with periodic timeout checks
    spdlog::info("Creating standard device files...");

    // Standard I/O devices
    CreateDeviceFileInternalLocked("/dev/null", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/zero", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/random", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/urandom", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/stdout", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/stderr", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/stdin", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/console", PS4FileType::Device);

    if (checkTimeout())
      return false;
    spdlog::info("Created standard I/O devices");

    // System management devices
    CreateDeviceFileInternalLocked("/dev/dipsw", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/hid", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/gc", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/rng", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/sbl_icc_mgr", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/sbl_core_mgr", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/sbl_crypto_mgr", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/sbl_auth_mgr", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/sbl_self_mgr", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/sandbox_mgr", PS4FileType::Device);

    if (checkTimeout())
      return false;
    spdlog::info("Created system management devices");

    // Hardware devices
    CreateDeviceFileInternalLocked("/dev/vce", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/dmem", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/sherlock", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/tty", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/ttyu0", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/ttyu1", PS4FileType::Device);

    if (checkTimeout())
      return false;
    spdlog::info("Created hardware interface devices");

    // Storage devices
    CreateDeviceFileInternalLocked("/dev/cd0", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/da0", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/nvme0", PS4FileType::Device);

    if (checkTimeout())
      return false;
    spdlog::info("Created storage devices");

    // Media and I/O devices
    CreateDeviceFileInternalLocked("/dev/gpu", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/hdmi", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/audioout", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/audioin", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/bluetooth", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/wlan", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/usb", PS4FileType::Device);

    if (checkTimeout())
      return false;
    spdlog::info("Created media and I/O devices");

    // Input devices
    CreateDeviceFileInternalLocked("/dev/camera", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/pad", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/touchpad", PS4FileType::Device);
    CreateDeviceFileInternalLocked("/dev/motion", PS4FileType::Device);

    if (checkTimeout())
      return false;
    spdlog::info("Created input devices");

    spdlog::info("Registering device handlers...");

    // Register essential device handlers in batches
    RegisterDeviceHandlerLocked(
        "/dev/null", [](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite)
            return true;
          if (!isWrite)
            std::memset(buffer, 0, size);
          return true;
        });

    RegisterDeviceHandlerLocked(
        "/dev/zero", [](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite)
            return true;
          std::memset(buffer, 0, size);
          return true;
        });

    RegisterDeviceHandlerLocked(
        "/dev/random", [](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite)
            return true;
          std::random_device rd;
          std::mt19937 gen(rd());
          std::uniform_int_distribution<int> dist(0, 255);
          uint8_t *buf = static_cast<uint8_t *>(buffer);
          for (size_t i = 0; i < size; i++) {
            buf[i] = dist(gen);
          }
          return true;
        });

    RegisterDeviceHandlerLocked(
        "/dev/urandom", [](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite)
            return true;
          std::random_device rd;
          std::mt19937 gen(rd());
          std::uniform_int_distribution<int> dist(0, 255);
          uint8_t *buf = static_cast<uint8_t *>(buffer);
          for (size_t i = 0; i < size; i++) {
            buf[i] = static_cast<uint8_t>(dist(gen));
          }
          return true;
        });

    if (checkTimeout())
      return false;
    spdlog::info("Registered basic I/O device handlers");

    RegisterDeviceHandlerLocked(
        "/dev/stdout", [](void *buffer, size_t size, bool isWrite) -> bool {
          if (!isWrite)
            return false;
          std::string output(static_cast<const char *>(buffer), size);
          spdlog::info("Device {}: {}", "/dev/stdout", output);
          return true;
        });

    RegisterDeviceHandlerLocked(
        "/dev/stderr", [](void *buffer, size_t size, bool isWrite) -> bool {
          if (!isWrite)
            return false;
          std::string output(static_cast<const char *>(buffer), size);
          spdlog::error("Device {}: {}", "/dev/stderr", output);
          return true;
        });

    RegisterDeviceHandlerLocked(
        "/dev/dipsw", [](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite)
            return false;
          if (size >= 4) {
            *static_cast<uint32_t *>(buffer) = 0;
          }
          return true;
        });

    if (checkTimeout())
      return false;
    spdlog::info("Registered standard I/O device handlers");

    RegisterDeviceHandlerLocked(
        "/dev/sbl_icc_mgr",
        [](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite) {
            spdlog::debug("ICC Manager: Command received ({} bytes)", size);
            return true;
          } else {
            if (size >= 4) {
              *static_cast<uint32_t *>(buffer) = 0; // Success
            }
            return true;
          }
        });

    RegisterDeviceHandlerLocked(
        "/dev/gc", [](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite) {
            spdlog::debug("GC: Command received ({} bytes)", size);
            return true;
          } else {
            if (size >= 4) {
              *static_cast<uint32_t *>(buffer) = 1; // Device ready
            }
            return true;
          }
        });

    RegisterDeviceHandlerLocked(
        "/dev/pad", [this](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite)
            return true;
          try {
            std::memset(buffer, 0, size);
            if (size >= 16) {
              static_cast<uint8_t *>(buffer)[0] = 1;
              // Simplified controller state - device ready
            }
            return true;
          } catch (const std::exception &e) {
            spdlog::error("Error accessing controller state: {}", e.what());
            return false;
          }
        });

    RegisterDeviceHandlerLocked(
        "/dev/audioout",
        [this](void *buffer, size_t size, bool isWrite) -> bool {
          if (isWrite) {
            // Simplified audio output - just acknowledge the write
            return true;
          } else {
            if (size >= 4) {
              *static_cast<uint32_t *>(buffer) = 1; // Device ready
            }
            return true;
          }
        });

    if (checkTimeout())
      return false;
    spdlog::info("Registered system device handlers");

    // Update statistics (mutex already held)
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Initialized {} device files with handlers",
                 m_deviceFiles.size());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("InitializeAllDeviceFilesLocked failed: {}", e.what());
    return false;
  }
}

// Initialize all device files (public interface)
bool PS4Filesystem::InitializeAllDeviceFiles() {
  COMPONENT_LOCK(m_mutex, "PS4Filesystem");
  return InitializeAllDeviceFilesLocked();
}

// Register a device handler (assumes mutex is already held)
bool PS4Filesystem::RegisterDeviceHandlerLocked(
    const std::string &devicePath,
    std::function<bool(void *, size_t, bool)> handler) {
  try {
    if (m_deviceFiles.find(devicePath) == m_deviceFiles.end()) {
      spdlog::error("RegisterDeviceHandlerLocked: Device not found: {}",
                    devicePath);
      return false;
    }
    m_deviceHandlers[devicePath] = handler;
    spdlog::debug("Registered handler for device: {}", devicePath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("RegisterDeviceHandlerLocked failed: {}", e.what());
    return false;
  }
}

// Register a device handler
bool PS4Filesystem::RegisterDeviceHandler(
    const std::string &devicePath,
    std::function<bool(void *, size_t, bool)> handler) {
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    if (m_deviceFiles.find(devicePath) == m_deviceFiles.end()) {
      spdlog::error("RegisterDeviceHandler: Device not found: {}", devicePath);
      return false;
    }
    m_deviceHandlers[devicePath] = handler;
    spdlog::debug("Registered handler for device: {}", devicePath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("RegisterDeviceHandler failed: {}", e.what());
    return false;
  }
}

// Handle device access
bool PS4Filesystem::HandleDeviceAccess(const std::string &path, void *buffer,
                                       size_t size, bool isWrite) {
  try {
    COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
    auto it = m_deviceHandlers.find(path);
    if (it != m_deviceHandlers.end()) {
      auto handler = it->second;
      lock.unlock();
      return handler(buffer, size, isWrite);
    }
    lock.unlock();

    if (path == "/dev/null" || path == "/dev/zero") {
      if (isWrite)
        return true;
      std::memset(buffer, 0, size);
      return true;
    } else if (path == "/dev/random" || path == "/dev/urandom" ||
               path == "/dev/rng") {
      if (isWrite)
        return true;
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<int> dis(0, 255);
      uint8_t *buf = static_cast<uint8_t *>(buffer);
      for (size_t i = 0; i < size; ++i) {
        buf[i] = static_cast<uint8_t>(dis(gen));
      }
      return true;
    } else if (path == "/dev/stdout" || path == "/dev/stderr" ||
               path == "/dev/console") {
      if (isWrite) {
        std::string output(static_cast<const char *>(buffer), size);
        if (path == "/dev/stderr") {
          spdlog::error("Device {}: {}", path, output);
        } else {
          spdlog::info("Device {}: {}", path, output);
        }
        return true;
      }
      return false;
    } else if (path == "/dev/stdin") {
      if (!isWrite) {
        if (size > 0) {
          static_cast<char *>(buffer)[0] = '\n';
          return true;
        }
      }
      return false;
    }

    spdlog::warn("Access to unhandled device: {}, isWrite={}, size={}", path,
                 isWrite, size);
    return false;
  } catch (const std::exception &e) {
    spdlog::error("HandleDeviceAccess failed for {}: {}", path, e.what());
    return false;
  }
}

// Determine file type
PS4FileType PS4Filesystem::DetermineFileType(const std::string &path) const {
  try {
    if (path.empty())
      return PS4FileType::Regular;
    if (path.back() == '/' || path.back() == '\\')
      return PS4FileType::Directory;
    if (m_deviceFiles.find(path) != m_deviceFiles.end())
      return PS4FileType::Device;

    size_t dotPos = path.find_last_of('.');
    if (dotPos == std::string::npos)
      return PS4FileType::Regular;
    std::string ext = path.substr(dotPos + 1);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    if (ext == "elf" || ext == "bin" || ext == "so" || ext == "dll" ||
        ext == "sprx" || ext == "pkg") {
      return PS4FileType::Regular;
    } else if (ext == "sfo") {
      return PS4FileType::System;
    } else if (ext == "txt" || ext == "log") {
      return PS4FileType::Regular;
    }
    return PS4FileType::Regular;
  } catch (const std::exception &e) {
    spdlog::error("DetermineFileType failed for {}: {}", path, e.what());
    return PS4FileType::Regular;
  }
}

// Validate PS4 permissions
bool PS4Filesystem::ValidatePS4Permissions(const std::string &path, int mode) {
  try {
    COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
    auto it = m_files.find(path);
    if (it == m_files.end()) {
      errno = ENOENT;
      return false;
    }
    const FileEntry &entry = it->second;
    if ((mode & O_RDONLY) && !(entry.mode & 0444))
      return false;
    if ((mode & O_WRONLY) && !(entry.mode & 0222))
      return false;
#ifndef O_EXEC
#define O_EXEC 0x040000
#endif
    if ((mode & O_EXEC) && !(entry.mode & 0111))
      return false;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidatePS4Permissions failed for {}: {}", path, e.what());
    errno = EACCES;
    return false;
  }
}

// Map virtual path to host path (assumes mutex is already held by caller)
std::string PS4Filesystem::MapToHostPathLocked(const std::string &virtualPath) {
  auto start = std::chrono::steady_clock::now();
  const auto timeout =
      std::chrono::milliseconds(100); // Short timeout for path mapping

  try {
    spdlog::debug("MapToHostPathLocked: Starting mapping for '{}'",
                  virtualPath);

    std::filesystem::path hostPath = m_mountPoints.GetHostPath(virtualPath);

    // Check timeout
    auto current = std::chrono::steady_clock::now();
    if (current - start > timeout) {
      spdlog::warn(
          "MapToHostPathLocked: Timeout while getting host path for '{}'",
          virtualPath);
      // Use fallback immediately
      std::filesystem::path rootPath(m_rootPath);
      std::string cleanVirtualPath = virtualPath;
      if (!cleanVirtualPath.empty() && cleanVirtualPath[0] == '/') {
        cleanVirtualPath = cleanVirtualPath.substr(1);
      }
      return (rootPath / cleanVirtualPath).string();
    }

    spdlog::debug(
        "MapToHostPathLocked: virtualPath='{}', mountPoint result='{}'",
        virtualPath, hostPath.string());
    if (hostPath.empty()) {
      std::filesystem::path rootPath(m_rootPath);
      std::string cleanVirtualPath = virtualPath;
      if (!cleanVirtualPath.empty() && cleanVirtualPath[0] == '/') {
        cleanVirtualPath = cleanVirtualPath.substr(1);
      }
      std::string result = (rootPath / cleanVirtualPath).string();
      spdlog::debug("MapToHostPathLocked: using fallback, result='{}'", result);
      return result;
    }
    spdlog::debug("MapToHostPathLocked: using mount point, result='{}'",
                  hostPath.string());
    return hostPath.string();
  } catch (const std::exception &e) {
    spdlog::error("MapToHostPathLocked failed for {}: {}", virtualPath,
                  e.what());
    // Return a safe fallback path
    std::filesystem::path rootPath(m_rootPath);
    std::string cleanVirtualPath = virtualPath;
    if (!cleanVirtualPath.empty() && cleanVirtualPath[0] == '/') {
      cleanVirtualPath = cleanVirtualPath.substr(1);
    }
    return (rootPath / cleanVirtualPath).string();
  }
}

// Map virtual path to host path
std::string PS4Filesystem::MapToHostPath(const std::string &virtualPath) {
  auto start = std::chrono::steady_clock::now();
  const auto timeout =
      std::chrono::milliseconds(100); // Short timeout for path mapping

  try {
    spdlog::debug("MapToHostPath: Starting mapping for '{}'", virtualPath);

    std::filesystem::path hostPath = m_mountPoints.GetHostPath(virtualPath);

    // Check timeout
    auto current = std::chrono::steady_clock::now();
    if (current - start > timeout) {
      spdlog::warn("MapToHostPath: Timeout while getting host path for '{}'",
                   virtualPath);
      // Use fallback immediately
      std::filesystem::path rootPath(m_rootPath);
      std::string cleanVirtualPath = virtualPath;
      if (!cleanVirtualPath.empty() && cleanVirtualPath[0] == '/') {
        cleanVirtualPath = cleanVirtualPath.substr(1);
      }
      return (rootPath / cleanVirtualPath).string();
    }

    spdlog::debug("MapToHostPath: virtualPath='{}', mountPoint result='{}'",
                  virtualPath, hostPath.string());
    if (hostPath.empty()) {
      std::filesystem::path rootPath(m_rootPath);
      std::string cleanVirtualPath = virtualPath;
      if (!cleanVirtualPath.empty() && cleanVirtualPath[0] == '/') {
        cleanVirtualPath = cleanVirtualPath.substr(1);
      }
      std::string result = (rootPath / cleanVirtualPath).string();
      spdlog::debug("MapToHostPath: using fallback, result='{}'", result);
      return result;
    }
    spdlog::debug("MapToHostPath: using mount point, result='{}'",
                  hostPath.string());
    return hostPath.string();
  } catch (const std::exception &e) {
    spdlog::error("MapToHostPath failed for {}: {}", virtualPath, e.what());
    // Return a safe fallback path
    std::filesystem::path rootPath(m_rootPath);
    std::string cleanVirtualPath = virtualPath;
    if (!cleanVirtualPath.empty() && cleanVirtualPath[0] == '/') {
      cleanVirtualPath = cleanVirtualPath.substr(1);
    }
    return (rootPath / cleanVirtualPath).string();
  }
}

// Resolve virtual path
std::string PS4Filesystem::ResolvePath(const std::string &virtualPath) const {
  if (virtualPath.empty())
    return m_rootPath;
  std::string cleanPath =
      virtualPath[0] == '/' ? virtualPath.substr(1) : virtualPath;
  if (m_rootPath.empty())
    return cleanPath;
  std::filesystem::path rootPath(m_rootPath);
  return (rootPath / cleanPath).string();
}

// Calculate checksum
std::vector<uint8_t>
PS4Filesystem::CalculateChecksum(const std::vector<uint8_t> &data) const {
  try {
    uint32_t crc = 0xFFFFFFFF;
    const uint32_t polynomial = 0xEDB88320;
    for (uint8_t byte : data) {
      crc ^= byte;
      for (int i = 0; i < 8; ++i) {
        if (crc & 1)
          crc = (crc >> 1) ^ polynomial;
        else
          crc >>= 1;
      }
    }
    crc ^= 0xFFFFFFFF;
    std::vector<uint8_t> checksum(4);
    checksum[0] = (crc >> 24) & 0xFF;
    checksum[1] = (crc >> 16) & 0xFF;
    checksum[2] = (crc >> 8) & 0xFF;
    checksum[3] = crc & 0xFF;
    return checksum;
  } catch (const std::exception &e) {
    spdlog::error("Checksum calculation failed: {}", e.what());
    return {};
  }
}

// Create device file internally (assumes mutex is already held)
bool PS4Filesystem::CreateDeviceFileInternalLocked(const std::string &path,
                                                   PS4FileType deviceType) {
  try {
    spdlog::debug("CreateDeviceFileInternalLocked: Starting for '{}'", path);

    // Get host path without acquiring mutex (caller already holds it)
    std::string hostPath = MapToHostPathLocked(path);
    spdlog::debug("CreateDeviceFileInternalLocked: Got host path '{}' for '{}'",
                  hostPath, path);

    // Create the device file entry (mutex already held by caller)
    m_deviceFiles[path] = deviceType;
    FileEntry &entry = m_files[path];
    entry.path = path;
    entry.hostPath = hostPath;
    entry.fileType = deviceType;
    entry.isDir = false;
    entry.present = true;
    entry.size = 0;
    entry.mode = 0666;
    entry.ps4Permissions = 0666;
    entry.creationTime = std::time(nullptr);
    entry.modificationTime = entry.creationTime;
    entry.accessTime = entry.creationTime;
    spdlog::debug(
        "CreateDeviceFileInternalLocked: Successfully created device file '{}'",
        path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Failed to create device file {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

// Create device file internally
bool PS4Filesystem::CreateDeviceFileInternal(const std::string &path,
                                             PS4FileType deviceType) {
  try {
    spdlog::debug("CreateDeviceFileInternal: Starting for '{}'", path);

    // Get host path first without holding the main mutex to avoid deadlock
    std::string hostPath = MapToHostPath(path);
    spdlog::debug("CreateDeviceFileInternal: Got host path '{}' for '{}'",
                  hostPath, path);

    // Now acquire the mutex and create the device file entry
    spdlog::debug("CreateDeviceFileInternal: Acquiring mutex for '{}'", path);
    COMPONENT_LOCK(m_mutex, "PS4Filesystem");
    spdlog::debug("CreateDeviceFileInternal: Mutex acquired for '{}'", path);

    m_deviceFiles[path] = deviceType;
    FileEntry &entry = m_files[path];
    entry.path = path;
    entry.hostPath = hostPath;
    entry.fileType = deviceType;
    entry.isDir = false;
    entry.present = true;
    entry.size = 0;
    entry.mode = 0666;
    entry.ps4Permissions = 0666;
    entry.creationTime = std::time(nullptr);
    entry.modificationTime = entry.creationTime;
    entry.accessTime = entry.creationTime;
    spdlog::debug(
        "CreateDeviceFileInternal: Successfully created device file '{}'",
        path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Failed to create device file {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

// Get PFS block
PFSBlock *PS4Filesystem::GetPFSBlock(PFSFile &file, uint64_t blockIndex,
                                     bool createIfNotExist) {
  auto blockIt = file.blocks.find(blockIndex);
  if (blockIt != file.blocks.end()) {
    blockIt->second.lastAccessTime = std::time(nullptr);
    file.cacheHits++;
    return &blockIt->second;
  }

  file.cacheMisses++;
  if (blockIndex >= file.blockCount) {
    if (!createIfNotExist)
      return nullptr;
    file.blockCount = blockIndex + 1;
    file.size = file.blockCount * file.blockSize;
    std::fstream fileStream(file.hostPath,
                            std::ios::binary | std::ios::in | std::ios::out);
    if (fileStream) {
      fileStream.seekp(16);
      fileStream.write(reinterpret_cast<const char *>(&file.size),
                       sizeof(file.size));
      fileStream.seekp(24);
      fileStream.write(reinterpret_cast<const char *>(&file.blockCount),
                       sizeof(file.blockCount));
      fileStream.seekp(0, std::ios::end);
      std::streampos currentSize = fileStream.tellp();
      uint64_t requiredSize = 256 + (file.blockCount * file.blockSize);
      if (currentSize < static_cast<std::streampos>(requiredSize)) {
        std::vector<uint8_t> emptyBlock(file.blockSize, 0);
        fileStream.seekp(currentSize);
        while (fileStream.tellp() < static_cast<std::streampos>(requiredSize)) {
          fileStream.write(reinterpret_cast<const char *>(emptyBlock.data()),
                           emptyBlock.size());
        }
      }
      fileStream.close();
    }
  }

  ManagePFSCache();

  PFSBlock newBlock;
  newBlock.index = blockIndex;
  newBlock.offset = 256 + (blockIndex * file.blockSize);
  newBlock.lastAccessTime = std::time(nullptr);
  newBlock.loaded = true;

  std::ifstream fileStream(file.hostPath, std::ios::binary);
  if (fileStream) {
    fileStream.seekg(newBlock.offset);
    newBlock.data.resize(file.blockSize);
    fileStream.read(reinterpret_cast<char *>(newBlock.data.data()),
                    file.blockSize);
    fileStream.close();
    if (file.isEncrypted && !file.key.empty()) {
      std::vector<uint8_t> decryptedData;
      std::vector<uint8_t> blockIv = file.iv;
      uint64_t indexValue = blockIndex;
      for (size_t i = 0; i < 8 && i < blockIv.size(); i++) {
        blockIv[i] ^= ((indexValue >> (i * 8)) & 0xFF);
      }
      if (!DecryptPfsData(newBlock.data, decryptedData, file.key, blockIv)) {
        spdlog::error("Failed to decrypt PFS block: file={}, block={}",
                      file.path, blockIndex);
        return nullptr;
      }
      newBlock.data = std::move(decryptedData);
    }
  } else {
    newBlock.data.resize(file.blockSize, 0);
    newBlock.dirty = true;
  }

  file.blocks[blockIndex] = newBlock;
  return &file.blocks[blockIndex];
}

// Flush PFS block
bool PS4Filesystem::FlushPFSBlock(PFSFile &file, PFSBlock &block) {
  if (!block.dirty)
    return true;
  try {
    std::fstream fileStream(file.hostPath,
                            std::ios::binary | std::ios::in | std::ios::out);
    if (!fileStream) {
      spdlog::error("FlushPFSBlock: Failed to open file: {}", file.hostPath);
      return false;
    }

    std::vector<uint8_t> dataToWrite = block.data;
    if (file.isEncrypted && !file.key.empty()) {
      std::vector<uint8_t> encryptedData;
      std::vector<uint8_t> blockIv = file.iv;
      uint64_t indexValue = block.index;
      for (size_t i = 0; i < 8 && i < blockIv.size(); i++) {
        blockIv[i] ^= ((indexValue >> (i * 8)) & 0xFF);
      }
      if (!EncryptPfsData(block.data, encryptedData, file.key, blockIv)) {
        spdlog::error("Failed to encrypt PFS block: file={}, block={}",
                      file.path, block.index);
        return false;
      }
      dataToWrite = std::move(encryptedData);
    }

    fileStream.seekp(block.offset);
    fileStream.write(reinterpret_cast<const char *>(dataToWrite.data()),
                     dataToWrite.size());
    block.dirty = false;
    block.checksum = CalculateChecksum(dataToWrite);
    return fileStream.good();
  } catch (const std::exception &e) {
    spdlog::error("FlushPFSBlock failed: {}", e.what());
    return false;
  }
}

// Manage PFS cache
void PS4Filesystem::ManagePFSCache() {
  uint64_t totalCacheSize = 0;
  uint64_t totalBlocks = 0;
  for (auto &filePair : m_pfsFiles) {
    totalBlocks += filePair.second.blocks.size();
    totalCacheSize += filePair.second.blocks.size() * filePair.second.blockSize;
  }
  if (totalCacheSize <= m_pfsCacheSize)
    return;
  spdlog::debug("PFS cache full ({} bytes, {} blocks), evicting least recently "
                "used blocks",
                totalCacheSize, totalBlocks);

  struct EvictionCandidate {
    PFSFile *file;
    uint64_t blockIndex;
    uint64_t lastAccessTime;
    EvictionCandidate(PFSFile *f, uint64_t bi, uint64_t lat)
        : file(f), blockIndex(bi), lastAccessTime(lat) {}
  };

  std::vector<EvictionCandidate> candidates;
  for (auto &filePair : m_pfsFiles) {
    for (auto &blockPair : filePair.second.blocks) {
      candidates.push_back(
          {&filePair.second, blockPair.first,
           static_cast<uint64_t>(blockPair.second.lastAccessTime)});
    }
  }

  std::sort(candidates.begin(), candidates.end(),
            [](const EvictionCandidate &a, const EvictionCandidate &b) {
              return a.lastAccessTime < b.lastAccessTime;
            });

  uint64_t bytesToFree = totalCacheSize - m_pfsCacheSize;
  uint64_t bytesFreed = 0;
  for (const auto &candidate : candidates) {
    auto &file = *candidate.file;
    auto &block = file.blocks[candidate.blockIndex];
    if (block.dirty) {
      if (!FlushPFSBlock(file, block)) {
        spdlog::error("Failed to flush dirty block during cache eviction: "
                      "file={}, block={}",
                      file.path, candidate.blockIndex);
        continue;
      }
    }
    bytesFreed += block.data.size();
    file.blocks.erase(candidate.blockIndex);
    if (bytesFreed >= bytesToFree)
      break;
  }
  spdlog::debug("Evicted {} bytes from PFS cache", bytesFreed);
}

// Encrypt PFS data
bool PS4Filesystem::EncryptPfsData(const std::vector<uint8_t> &plaintext,
                                   std::vector<uint8_t> &ciphertext,
                                   const std::vector<uint8_t> &key,
                                   const std::vector<uint8_t> &iv) {
  try {
    if (key.size() != 32 || iv.size() != 16) {
      spdlog::error("EncryptPfsData: Invalid key or IV size");
      return false;
    }

    size_t paddedSize = (plaintext.size() + 15) & ~15;
    std::vector<uint8_t> paddedData = plaintext;
    paddedData.resize(paddedSize, 0);

    // Use EVP API instead of deprecated AES functions
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
      spdlog::error("EncryptPfsData: Failed to create cipher context");
      return false;
    }

    std::vector<uint8_t> ivCopy = iv;
    if (EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), nullptr, key.data(), ivCopy.data()) != 1) {
      EVP_CIPHER_CTX_free(ctx);
      spdlog::error("EncryptPfsData: Failed to initialize encryption");
      return false;
    }

    ciphertext.resize(paddedSize);
    int len;
    if (EVP_EncryptUpdate(ctx, ciphertext.data(), &len, paddedData.data(), static_cast<int>(paddedSize)) != 1) {
      EVP_CIPHER_CTX_free(ctx);
      spdlog::error("EncryptPfsData: Failed to encrypt data");
      return false;
    }

    int final_len;
    if (EVP_EncryptFinal_ex(ctx, ciphertext.data() + len, &final_len) != 1) {
      EVP_CIPHER_CTX_free(ctx);
      spdlog::error("EncryptPfsData: Failed to finalize encryption");
      return false;
    }

    ciphertext.resize(len + final_len);
    EVP_CIPHER_CTX_free(ctx);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("EncryptPfsData failed: {}", e.what());
    return false;
  }
}

// Decrypt PFS data
bool PS4Filesystem::DecryptPfsData(const std::vector<uint8_t> &ciphertext,
                                   std::vector<uint8_t> &plaintext,
                                   const std::vector<uint8_t> &key,
                                   const std::vector<uint8_t> &iv) {
  try {
    if (key.size() != 32 || iv.size() != 16) {
      spdlog::error("DecryptPfsData: Invalid key or IV size");
      return false;
    }

    if (ciphertext.size() % 16 != 0) {
      spdlog::error(
          "DecryptPfsData: Ciphertext size not multiple of block size");
      return false;
    }

    // Use EVP API instead of deprecated AES functions
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
      spdlog::error("DecryptPfsData: Failed to create cipher context");
      return false;
    }

    std::vector<uint8_t> ivCopy = iv;
    if (EVP_DecryptInit_ex(ctx, EVP_aes_256_cbc(), nullptr, key.data(), ivCopy.data()) != 1) {
      EVP_CIPHER_CTX_free(ctx);
      spdlog::error("DecryptPfsData: Failed to initialize decryption");
      return false;
    }

    plaintext.resize(ciphertext.size());
    int len;
    if (EVP_DecryptUpdate(ctx, plaintext.data(), &len, ciphertext.data(), static_cast<int>(ciphertext.size())) != 1) {
      EVP_CIPHER_CTX_free(ctx);
      spdlog::error("DecryptPfsData: Failed to decrypt data");
      return false;
    }

    int final_len;
    if (EVP_DecryptFinal_ex(ctx, plaintext.data() + len, &final_len) != 1) {
      EVP_CIPHER_CTX_free(ctx);
      spdlog::error("DecryptPfsData: Failed to finalize decryption");
      return false;
    }

    plaintext.resize(len + final_len);
    EVP_CIPHER_CTX_free(ctx);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("DecryptPfsData failed: {}", e.what());
    return false;
  }
}

// Generate PFS key
bool PS4Filesystem::GeneratePFSKey(std::vector<uint8_t> &key,
                                   const std::string &passphrase) {
  try {
    key.resize(32);
    if (passphrase.empty()) {
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_int_distribution<int> dist(0, 255);
      for (size_t i = 0; i < key.size(); i++) {
        key[i] = dist(gen);
      }
    } else {
      std::string salt = "PS4_PFS_SALT";
      std::string material = passphrase + salt;
      for (size_t i = 0; i < 1000; i++) {
        material = material + std::to_string(i);
        std::hash<std::string> hasher;
        size_t hash = hasher(material);
        *reinterpret_cast<size_t *>(
            &key[i % (key.size() - sizeof(size_t) + 1)]) ^= hash;
      }
    }
    return true;
  } catch (const std::exception &e) {
    spdlog::error("GeneratePFSKey failed: {}", e.what());
    return false;
  }
}

// Get game directory
std::string PS4Filesystem::GetGameDirectory() const {
  COMPONENT_SHARED_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::string result =
        m_gameDirectory.empty() ? m_rootPath + "/app0" : m_gameDirectory;
    spdlog::trace("GetGameDirectory: {}", result);
    return result;
  } catch (const std::exception &e) {
    spdlog::error("GetGameDirectory failed: {}", e.what());
    return m_rootPath + "/app0";
  }
}

// Read file into a vector
bool PS4Filesystem::ReadFile(const std::string &path,
                             std::vector<uint8_t> &data) {
  auto start = std::chrono::steady_clock::now();

  // Try cache first (without holding the main mutex)
  if (GetCachedFile(path, data)) {
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("ReadFile: Cache hit for {} ({} bytes)", path, data.size());
    return true;
  }

  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("ReadFile: Invalid path: {}", path);
      errno = ENOENT;
      return false;
    }

    std::ifstream file(hostPath, std::ios::binary);
    if (!file) {
      spdlog::error("ReadFile: Failed to open: {}", hostPath.string());
      errno = EIO;
      return false;
    }

    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);
    data.resize(size);
    file.read(reinterpret_cast<char *>(data.data()), size);

    // Cache the file data for future access
    std::vector<uint8_t> cacheData = data; // Copy for caching
    CacheFile(path, std::move(cacheData));

    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("ReadFile: Read and cached {} bytes from {}", size, path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ReadFile failed for {}: {}", path, e.what());
    errno = EIO;
    return false;
  }
}

// Write data to a file
bool PS4Filesystem::WriteFile(const std::string &path, const void *data,
                              uint64_t size) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      spdlog::error("WriteFile: Invalid path: {}", path);
      errno = ENOENT;
      return false;
    }
    std::ofstream file(hostPath, std::ios::binary);
    if (!file) {
      spdlog::error("WriteFile: Failed to open: {}", hostPath.string());
      errno = EIO;
      m_stats.cacheMisses++;
      return false;
    }
    file.write(static_cast<const char *>(data), size);

    // Invalidate cache entry since file was modified
    InvalidateCachedFile(path);

    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("WriteFile: Wrote {} bytes to {} and invalidated cache", size,
                  path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("WriteFile failed for {}: {}", path, e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return false;
  }
}

// Create a virtual directory
bool PS4Filesystem::CreateVirtualDirectory(const std::string &path) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    std::filesystem::path hostPath = m_mountPoints.GetHostPath(path);
    if (hostPath.empty()) {
      hostPath = ResolvePath(path);
      spdlog::debug("CreateVirtualDirectory: Using resolved path: {}",
                    hostPath.string());
    }
    bool success = std::filesystem::create_directories(hostPath);
    if (success) {
      m_directories.push_back(path);
      FileEntry &entry = m_files[path];
      entry.path = path;
      entry.hostPath = hostPath.string();
      entry.size = 0;
      entry.mode = m_settings.defaultDirMode;
      entry.creationTime = std::time(nullptr);
      entry.modificationTime = entry.creationTime;
      entry.accessTime = entry.creationTime;
      entry.isDir = true;
      entry.fileType = PS4FileType::Directory;
      entry.cacheHits++;
      m_stats.operationCount++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("Created virtual directory: {}", path);
      return true;
    }
    spdlog::error("CreateVirtualDirectory: Failed to create directory: {}",
                  hostPath.string());
    errno = EIO;
    m_stats.cacheMisses++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("CreateVirtualDirectory failed for {}: {}", path, e.what());
    errno = EIO;
    m_stats.cacheMisses++;
    return false;
  }
}

// Set game directory
void PS4Filesystem::SetGameDirectory(const std::string &path) {
  auto start = std::chrono::steady_clock::now();
  COMPONENT_LOCK(m_mutex, "FilesystemMutex");
  try {
    m_gameDirectory = path;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Game directory set to: {}", path);
  } catch (const std::exception &e) {
    spdlog::error("SetGameDirectory failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

// PS4Filesystem Cache Management Implementation
void PS4Filesystem::InitializeCache(uint64_t maxCacheSize) {
  try {
    m_fileCache = std::make_unique<FileLRUCache>(maxCacheSize);
    m_stats.maxCacheSize = maxCacheSize;
    spdlog::info("PS4Filesystem cache initialized with {} bytes", maxCacheSize);
  } catch (const std::exception &e) {
    spdlog::error("Failed to initialize filesystem cache: {}", e.what());
    m_fileCache = nullptr;
  }
}

void PS4Filesystem::ShutdownCache() {
  if (m_fileCache) {
    spdlog::info("Shutting down filesystem cache - Hit ratio: {:.2f}%",
                 GetCacheHitRatio() * 100.0);
    m_fileCache.reset();
  }
}

bool PS4Filesystem::GetCachedFile(const std::string &path,
                                  std::vector<uint8_t> &data) {
  if (!m_fileCache) {
    return false;
  }

  bool hit = m_fileCache->Get(path, data);
  if (hit) {
    m_stats.cacheHits++;
  } else {
    m_stats.cacheMisses++;
  }

  return hit;
}

void PS4Filesystem::CacheFile(const std::string &path,
                              std::vector<uint8_t> &&data) {
  if (!m_fileCache) {
    return;
  }

  m_fileCache->Put(path, std::move(data));
  m_stats.cacheSize = m_fileCache->GetCurrentSize();
}

void PS4Filesystem::InvalidateCachedFile(const std::string &path) {
  if (m_fileCache) {
    m_fileCache->InvalidateFile(path);
    m_stats.cacheSize = m_fileCache->GetCurrentSize();
  }
}

void PS4Filesystem::ClearCache() {
  if (m_fileCache) {
    m_fileCache->Clear();
    m_stats.cacheSize = 0;
    spdlog::info("Filesystem cache cleared");
  }
}

void PS4Filesystem::SetCacheSize(uint64_t maxSize) {
  if (m_fileCache) {
    m_fileCache->SetMaxSize(maxSize);
    m_stats.maxCacheSize = maxSize;
    m_stats.cacheSize = m_fileCache->GetCurrentSize();
  }
}

uint64_t PS4Filesystem::GetCacheSize() const {
  return m_fileCache ? m_fileCache->GetMaxSize() : 0;
}

uint64_t PS4Filesystem::GetCacheUsage() const {
  return m_fileCache ? m_fileCache->GetCurrentSize() : 0;
}

double PS4Filesystem::GetCacheHitRatio() const {
  return m_fileCache ? m_fileCache->GetHitRatio() : 0.0;
}

// FileLRUCache Implementation
FileLRUCache::FileLRUCache(uint64_t maxSize)
    : maxCacheSize(maxSize), currentCacheSize(0), hitCount(0), missCount(0),
      evictionCount(0) {
  // Create dummy head and tail nodes for the doubly-linked list
  head = std::make_shared<CacheNode>("", FileCacheEntry());
  tail = std::make_shared<CacheNode>("", FileCacheEntry());
  head->next = tail;
  tail->prev = head;

  spdlog::info("FileLRUCache initialized with max size: {} bytes", maxSize);
}

bool FileLRUCache::Get(const std::string &filePath,
                       std::vector<uint8_t> &data) {
  std::unique_lock<std::shared_mutex> lock(cacheMutex);

  auto it = cacheMap.find(filePath);
  if (it == cacheMap.end()) {
    missCount++;
    return false;
  }

  // Move to front (most recently used)
  CacheNodePtr node = it->second;
  MoveToFront(node);

  // Update access statistics
  node->entry.lastAccessed = std::chrono::steady_clock::now();
  node->entry.accessCount++;

  // Copy data
  data = node->entry.data;
  hitCount++;

  spdlog::trace("Cache hit for file: {}", filePath);
  return true;
}

void FileLRUCache::Put(const std::string &filePath,
                       std::vector<uint8_t> &&data) {
  std::unique_lock<std::shared_mutex> lock(cacheMutex);

  auto it = cacheMap.find(filePath);
  if (it != cacheMap.end()) {
    // Update existing entry
    CacheNodePtr node = it->second;

    // Update size tracking
    currentCacheSize -= node->entry.fileSize;
    currentCacheSize += data.size();

    // Update entry
    node->entry.data = std::move(data);
    node->entry.fileSize = node->entry.data.size();
    node->entry.lastAccessed = std::chrono::steady_clock::now();
    node->entry.lastModified = node->entry.lastAccessed;
    node->entry.accessCount++;
    node->entry.isDirty = false;

    // Move to front
    MoveToFront(node);
  } else {
    // Create new entry
    FileCacheEntry entry(filePath, std::move(data));
    CacheNodePtr newNode =
        std::make_shared<CacheNode>(filePath, std::move(entry));

    // Add to map and list
    cacheMap[filePath] = newNode;
    AddToFront(newNode);
    currentCacheSize += newNode->entry.fileSize;
  }

  // Enforce size limit
  EnforceMaxSize();

  spdlog::trace("Cached file: {} ({} bytes)", filePath, data.size());
}

void FileLRUCache::Remove(const std::string &filePath) {
  std::unique_lock<std::shared_mutex> lock(cacheMutex);

  auto it = cacheMap.find(filePath);
  if (it != cacheMap.end()) {
    CacheNodePtr node = it->second;
    currentCacheSize -= node->entry.fileSize;

    RemoveNode(node);
    cacheMap.erase(it);

    spdlog::trace("Removed file from cache: {}", filePath);
  }
}

void FileLRUCache::Clear() {
  std::unique_lock<std::shared_mutex> lock(cacheMutex);

  cacheMap.clear();
  head->next = tail;
  tail->prev = head;
  currentCacheSize = 0;

  spdlog::info("Cache cleared");
}

void FileLRUCache::SetMaxSize(uint64_t maxSize) {
  std::unique_lock<std::shared_mutex> lock(cacheMutex);

  maxCacheSize = maxSize;
  EnforceMaxSize();

  spdlog::info("Cache max size set to: {} bytes", maxSize);
}

void FileLRUCache::InvalidateFile(const std::string &filePath) {
  Remove(filePath);
}

void FileLRUCache::InvalidatePattern(const std::string &pattern) {
  std::unique_lock<std::shared_mutex> lock(cacheMutex);

  std::vector<std::string> toRemove;
  for (const auto &pair : cacheMap) {
    if (pair.first.find(pattern) != std::string::npos) {
      toRemove.push_back(pair.first);
    }
  }

  lock.unlock();

  for (const auto &path : toRemove) {
    Remove(path);
  }

  spdlog::info("Invalidated {} files matching pattern: {}", toRemove.size(),
               pattern);
}

bool FileLRUCache::IsFileCached(const std::string &filePath) const {
  std::shared_lock<std::shared_mutex> lock(cacheMutex);
  return cacheMap.find(filePath) != cacheMap.end();
}

double FileLRUCache::GetHitRatio() const {
  std::shared_lock<std::shared_mutex> lock(cacheMutex);
  uint64_t total = hitCount + missCount;
  return total > 0 ? static_cast<double>(hitCount) / total : 0.0;
}

void FileLRUCache::EvictLeastRecentlyUsed() {
  std::unique_lock<std::shared_mutex> lock(cacheMutex);

  if (cacheMap.empty()) {
    return;
  }

  CacheNodePtr lru = RemoveLast();
  if (lru && lru != head && lru != tail) {
    currentCacheSize -= lru->entry.fileSize;
    cacheMap.erase(lru->key);
    evictionCount++;

    spdlog::trace("Evicted LRU file: {}", lru->key);
  }
}

void FileLRUCache::EvictOldEntries(std::chrono::seconds maxAge) {
  std::unique_lock<std::shared_mutex> lock(cacheMutex);

  auto now = std::chrono::steady_clock::now();
  std::vector<std::string> toRemove;

  for (const auto &pair : cacheMap) {
    auto age = std::chrono::duration_cast<std::chrono::seconds>(
        now - pair.second->entry.lastAccessed);
    if (age > maxAge) {
      toRemove.push_back(pair.first);
    }
  }

  lock.unlock();

  for (const auto &path : toRemove) {
    Remove(path);
  }

  spdlog::info("Evicted {} old cache entries", toRemove.size());
}

void FileLRUCache::CompactCache() {
  // For now, just evict old entries
  EvictOldEntries(std::chrono::minutes(30));
}

void FileLRUCache::MoveToFront(CacheNodePtr node) {
  if (!node || node == head->next) {
    return; // Already at front
  }

  RemoveNode(node);
  AddToFront(node);
}

void FileLRUCache::RemoveNode(CacheNodePtr node) {
  if (!node || !node->prev || !node->next) {
    return;
  }

  node->prev->next = node->next;
  node->next->prev = node->prev;
}

void FileLRUCache::AddToFront(CacheNodePtr node) {
  if (!node) {
    return;
  }

  node->prev = head;
  node->next = head->next;
  head->next->prev = node;
  head->next = node;
}

FileLRUCache::CacheNodePtr FileLRUCache::RemoveLast() {
  if (tail->prev == head) {
    return nullptr; // Empty list
  }

  CacheNodePtr last = tail->prev;
  RemoveNode(last);
  return last;
}

void FileLRUCache::EnforceMaxSize() {
  while (currentCacheSize > maxCacheSize && !cacheMap.empty()) {
    EvictLeastRecentlyUsed();
  }
}

bool FileLRUCache::ShouldEvict(const FileCacheEntry &entry) const {
  // Simple eviction policy - could be enhanced with more sophisticated logic
  auto now = std::chrono::steady_clock::now();
  auto age = std::chrono::duration_cast<std::chrono::minutes>(
      now - entry.lastAccessed);

  // Evict if older than 30 minutes and accessed less than 3 times
  return age > std::chrono::minutes(30) && entry.accessCount < 3;
}

} // namespace ps4

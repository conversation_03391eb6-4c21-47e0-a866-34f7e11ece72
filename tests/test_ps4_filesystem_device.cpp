/**
 * @file test_ps4_filesystem_device.cpp
 * @brief Tests for PS4 filesystem device file functionality
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "utils/test_fixtures.h"
#include <fcntl.h>

using namespace ps4;
using namespace ps4::test;

class PS4FilesystemDeviceTest : public DeviceFileTestFixture {
protected:
    void SetUp() override {
        DeviceFileTestFixture::SetUp();
    }
};

// Device file initialization tests
TEST_F(PS4FilesystemDeviceTest, InitializeAllDeviceFiles) {
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());
}

// Device file type determination tests
TEST_F(PS4FilesystemDeviceTest, DetermineFileType) {
    // Test various device paths
    EXPECT_EQ(GetFilesystem()->DetermineFileType("/dev/null"), PS4FileType::Device);
    EXPECT_EQ(GetFilesystem()->DetermineFileType("/dev/zero"), PS4FileType::Device);
    EXPECT_EQ(GetFilesystem()->DetermineFileType("/dev/random"), PS4FileType::Device);
    
    // Test regular file paths
    EXPECT_EQ(GetFilesystem()->DetermineFileType("/app0/file.bin"), PS4FileType::Regular);
    EXPECT_EQ(GetFilesystem()->DetermineFileType("/savedata/save.dat"), PS4FileType::Regular);
    
    // Test directory paths
    EXPECT_EQ(GetFilesystem()->DetermineFileType("/app0/"), PS4FileType::Directory);
    EXPECT_EQ(GetFilesystem()->DetermineFileType("/system/"), PS4FileType::Directory);
}

// Device handler registration tests
TEST_F(PS4FilesystemDeviceTest, RegisterDeviceHandler) {
    bool handler_called = false;
    
    // Register a test device handler
    auto test_handler = [&handler_called](void* buffer, size_t size, bool is_write) -> bool {
        handler_called = true;
        if (!is_write && buffer && size > 0) {
            // For read operations, fill buffer with test data
            memset(buffer, 0xAB, size);
        }
        return true;
    };
    
    EXPECT_TRUE(GetFilesystem()->RegisterDeviceHandler("/dev/test", test_handler));
    
    // Test device access
    std::vector<uint8_t> buffer(100);
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/test", buffer.data(), buffer.size(), false));
    EXPECT_TRUE(handler_called);
    
    // Verify buffer was filled
    for (uint8_t byte : buffer) {
        EXPECT_EQ(byte, 0xAB);
    }
}

// Device access tests
TEST_F(PS4FilesystemDeviceTest, HandleDeviceAccess) {
    // Initialize device files
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());
    
    // Test reading from /dev/zero (should return zeros)
    std::vector<uint8_t> zero_buffer(100, 0xFF); // Fill with non-zero first
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/zero", zero_buffer.data(), zero_buffer.size(), false));
    
    // Buffer should now contain zeros
    for (uint8_t byte : zero_buffer) {
        EXPECT_EQ(byte, 0x00);
    }
    
    // Test writing to /dev/null (should always succeed)
    std::vector<uint8_t> write_data = GenerateTestData(50, 0xCD);
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/null", write_data.data(), write_data.size(), true));
}

// Device file validation tests
TEST_F(PS4FilesystemDeviceTest, ValidatePS4Permissions) {
    // Test device file permissions
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/dev/null", O_WRONLY));
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/dev/zero", O_RDONLY));
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/dev/random", O_RDONLY));
    
    // Test regular file permissions
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/app0/file.bin", O_RDONLY));
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/savedata/save.dat", O_RDWR));
}

// Device file error handling tests
TEST_F(PS4FilesystemDeviceTest, DeviceErrorHandling) {
    // Test access to non-existent device
    std::vector<uint8_t> buffer(100);
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/dev/nonexistent", buffer.data(), buffer.size(), false));
    
    // Test invalid parameters
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/dev/null", nullptr, 100, false));
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("", buffer.data(), buffer.size(), false));
}

// Multiple device handler tests
TEST_F(PS4FilesystemDeviceTest, MultipleDeviceHandlers) {
    std::vector<bool> handler_calls(3, false);
    
    // Register multiple device handlers
    for (int i = 0; i < 3; ++i) {
        std::string device_path = "/dev/test" + std::to_string(i);
        auto handler = [&handler_calls, i](void* buffer, size_t size, bool is_write) -> bool {
            handler_calls[i] = true;
            if (!is_write && buffer && size > 0) {
                memset(buffer, 0x10 + i, size);
            }
            return true;
        };
        
        EXPECT_TRUE(GetFilesystem()->RegisterDeviceHandler(device_path, handler));
    }
    
    // Test each device
    for (int i = 0; i < 3; ++i) {
        std::string device_path = "/dev/test" + std::to_string(i);
        std::vector<uint8_t> buffer(10);
        
        EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess(device_path, buffer.data(), buffer.size(), false));
        EXPECT_TRUE(handler_calls[i]);
        
        // Verify correct handler was called
        for (uint8_t byte : buffer) {
            EXPECT_EQ(byte, 0x10 + i);
        }
    }
}

// Device handler replacement tests
TEST_F(PS4FilesystemDeviceTest, ReplaceDeviceHandler) {
    bool first_handler_called = false;
    bool second_handler_called = false;
    
    // Register first handler
    auto first_handler = [&first_handler_called](void* buffer, size_t size, bool is_write) -> bool {
        first_handler_called = true;
        return true;
    };
    
    EXPECT_TRUE(GetFilesystem()->RegisterDeviceHandler("/dev/replace_test", first_handler));
    
    // Register second handler (should replace first)
    auto second_handler = [&second_handler_called](void* buffer, size_t size, bool is_write) -> bool {
        second_handler_called = true;
        return true;
    };
    
    EXPECT_TRUE(GetFilesystem()->RegisterDeviceHandler("/dev/replace_test", second_handler));
    
    // Test device access - only second handler should be called
    std::vector<uint8_t> buffer(10);
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/replace_test", buffer.data(), buffer.size(), false));
    
    EXPECT_FALSE(first_handler_called);
    EXPECT_TRUE(second_handler_called);
}

// Standard PS4 device files tests
TEST_F(PS4FilesystemDeviceTest, StandardPS4DeviceFiles) {
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());

    // Test standard PS4 device files
    std::vector<std::string> standard_devices = {
        "/dev/null",
        "/dev/zero",
        "/dev/random",
        "/dev/console",
        "/dev/deci_tty0",
        "/dev/deci_tty1",
        "/dev/deci_tty2",
        "/dev/deci_tty3",
        "/dev/deci_tty4",
        "/dev/deci_tty5",
        "/dev/deci_tty6",
        "/dev/deci_tty7"
    };

    for (const auto& device : standard_devices) {
        // Verify device type is correctly identified
        PS4FileType type = GetFilesystem()->DetermineFileType(device);
        EXPECT_EQ(type, PS4FileType::Device) << "Device: " << device;

        // Test basic device access (reading)
        std::vector<uint8_t> buffer(64);
        bool read_success = GetFilesystem()->HandleDeviceAccess(device, buffer.data(), buffer.size(), false);

        // Some devices should always succeed (like /dev/null, /dev/zero)
        if (device == "/dev/null" || device == "/dev/zero") {
            EXPECT_TRUE(read_success) << "Device: " << device;
        }

        // Test writing to devices that support it
        if (device == "/dev/null" || device.find("/dev/deci_tty") != std::string::npos || device == "/dev/console") {
            std::vector<uint8_t> write_data = GenerateTestData(32, 0xAB);
            bool write_success = GetFilesystem()->HandleDeviceAccess(device, write_data.data(), write_data.size(), true);
            EXPECT_TRUE(write_success) << "Device write: " << device;
        }
    }
}

// Device file integration with file operations
TEST_F(PS4FilesystemDeviceTest, DeviceFileIntegrationWithFileOperations) {
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());

    // Test opening device files through regular file operations
    int null_fd = GetFilesystem()->OpenFile("/dev/null", O_WRONLY, 0);
    EXPECT_GT(null_fd, 0);

    int zero_fd = GetFilesystem()->OpenFile("/dev/zero", O_RDONLY, 0);
    EXPECT_GT(zero_fd, 0);

    // Test writing to /dev/null through file operations
    if (null_fd > 0) {
        std::vector<uint8_t> test_data = GenerateTestData(256, 0xCD);
        ssize_t bytes_written = GetFilesystem()->WriteFile(null_fd, test_data.data(), test_data.size());
        EXPECT_EQ(bytes_written, static_cast<ssize_t>(test_data.size()));

        GetFilesystem()->CloseFile(null_fd);
    }

    // Test reading from /dev/zero through file operations
    if (zero_fd > 0) {
        std::vector<uint8_t> buffer(256, 0xFF); // Fill with non-zero first
        ssize_t bytes_read = GetFilesystem()->ReadFile(zero_fd, buffer.data(), buffer.size());
        EXPECT_EQ(bytes_read, static_cast<ssize_t>(buffer.size()));

        // Buffer should now contain zeros
        for (uint8_t byte : buffer) {
            EXPECT_EQ(byte, 0x00);
        }

        GetFilesystem()->CloseFile(zero_fd);
    }

    // Test seeking on device files (should fail or be ignored)
    int console_fd = GetFilesystem()->OpenFile("/dev/console", O_RDWR, 0);
    if (console_fd > 0) {
        off_t seek_result = GetFilesystem()->SeekFile(console_fd, 100, SEEK_SET);
        // Seeking on device files typically fails or returns 0
        EXPECT_LE(seek_result, 0);

        GetFilesystem()->CloseFile(console_fd);
    }
}

// Concurrent device access tests
TEST_F(PS4FilesystemDeviceTest, ConcurrentDeviceAccess) {
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());

    const int num_threads = 4;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    std::atomic<int> successful_operations{0};
    std::atomic<bool> error_occurred{false};

    // Test concurrent access to /dev/null (should always succeed)
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &successful_operations, &error_occurred]() {
            try {
                for (int i = 0; i < operations_per_thread; ++i) {
                    // Alternate between different devices
                    std::string device = (i % 2 == 0) ? "/dev/null" : "/dev/zero";
                    bool is_write = (device == "/dev/null");

                    std::vector<uint8_t> buffer(64);
                    if (is_write) {
                        // Fill buffer with test data for writing
                        std::fill(buffer.begin(), buffer.end(), static_cast<uint8_t>(t + i));
                    }

                    bool success = GetFilesystem()->HandleDeviceAccess(device, buffer.data(), buffer.size(), is_write);
                    if (success) {
                        successful_operations++;

                        // For /dev/zero reads, verify we got zeros
                        if (!is_write) {
                            for (uint8_t byte : buffer) {
                                if (byte != 0x00) {
                                    error_occurred = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            } catch (const std::exception& e) {
                (void)e; // Mark as used to avoid compiler warning
                error_occurred = true;
            }
        });
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    EXPECT_FALSE(error_occurred.load()) << "Error occurred during concurrent device access";
    EXPECT_GT(successful_operations.load(), 0) << "No successful device operations completed";
}

// Device file permissions and security tests
TEST_F(PS4FilesystemDeviceTest, DeviceFilePermissionsAndSecurity) {
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());

    // Test read-only device permissions
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/dev/zero", O_RDONLY));
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/dev/random", O_RDONLY));

    // Test write-only device permissions
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/dev/null", O_WRONLY));

    // Test read-write device permissions
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/dev/console", O_RDWR));
    EXPECT_TRUE(GetFilesystem()->ValidatePS4Permissions("/dev/deci_tty0", O_RDWR));

    // Test invalid permission combinations (these should be caught by validation)
    // Note: The actual behavior depends on the implementation

    // Test access control for sensitive devices
    std::vector<std::string> sensitive_devices = {
        "/dev/kmem",      // Kernel memory (if implemented)
        "/dev/mem",       // Physical memory (if implemented)
        "/dev/port"       // I/O ports (if implemented)
    };

    for (const auto& device : sensitive_devices) {
        // These devices should either not exist or have strict access controls
        PS4FileType type = GetFilesystem()->DetermineFileType(device);
        if (type == PS4FileType::Device) {
            // If the device exists, test that it has appropriate restrictions
            std::vector<uint8_t> buffer(64);
            bool read_access = GetFilesystem()->HandleDeviceAccess(device, buffer.data(), buffer.size(), false);
            bool write_access = GetFilesystem()->HandleDeviceAccess(device, buffer.data(), buffer.size(), true);

            // Sensitive devices should typically deny access or require special permissions
            // The exact behavior depends on the security model
        }
    }
}

// Special device behaviors tests
TEST_F(PS4FilesystemDeviceTest, SpecialDeviceBehaviors) {
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());

    // Test /dev/zero behavior - should always return zeros
    std::vector<uint8_t> zero_buffer(1024, 0xFF); // Fill with non-zero
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/zero", zero_buffer.data(), zero_buffer.size(), false));

    for (uint8_t byte : zero_buffer) {
        EXPECT_EQ(byte, 0x00) << "/dev/zero should return only zeros";
    }

    // Test /dev/null behavior - should accept any write and discard it
    std::vector<uint8_t> null_data = GenerateTestData(2048, 0xAB);
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/null", null_data.data(), null_data.size(), true));

    // Multiple writes to /dev/null should all succeed
    for (int i = 0; i < 10; ++i) {
        std::vector<uint8_t> test_data = GenerateTestData(256, static_cast<uint8_t>(i));
        EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/null", test_data.data(), test_data.size(), true));
    }

    // Test /dev/random behavior - should return random-looking data
    std::vector<uint8_t> random_buffer1(256);
    std::vector<uint8_t> random_buffer2(256);

    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/random", random_buffer1.data(), random_buffer1.size(), false));
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/random", random_buffer2.data(), random_buffer2.size(), false));

    // The two random reads should be different (with very high probability)
    EXPECT_NE(random_buffer1, random_buffer2) << "/dev/random should return different data on successive reads";

    // Test console device behavior
    std::vector<uint8_t> console_write_data = {'H', 'e', 'l', 'l', 'o', '\n'};
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/console", console_write_data.data(), console_write_data.size(), true));

    // Test TTY device behavior
    std::vector<uint8_t> tty_write_data = {'T', 'T', 'Y', ' ', 'T', 'e', 's', 't', '\n'};
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/deci_tty0", tty_write_data.data(), tty_write_data.size(), true));
}

// Device file statistics and monitoring tests
TEST_F(PS4FilesystemDeviceTest, DeviceFileStatisticsAndMonitoring) {
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());

    // Register a custom device with statistics tracking
    std::atomic<int> read_count{0};
    std::atomic<int> write_count{0};
    std::atomic<size_t> bytes_read{0};
    std::atomic<size_t> bytes_written{0};

    auto stats_handler = [&](void* buffer, size_t size, bool is_write) -> bool {
        if (is_write) {
            write_count++;
            bytes_written += size;
            // Simulate successful write
            return true;
        } else {
            read_count++;
            bytes_read += size;
            // Fill buffer with test pattern
            if (buffer && size > 0) {
                memset(buffer, 0xAB, size);
            }
            return true;
        }
    };

    EXPECT_TRUE(GetFilesystem()->RegisterDeviceHandler("/dev/stats_test", stats_handler));

    // Perform various operations and track statistics
    const int num_operations = 50;
    for (int i = 0; i < num_operations; ++i) {
        // Alternate between reads and writes
        if (i % 2 == 0) {
            std::vector<uint8_t> read_buffer(128);
            GetFilesystem()->HandleDeviceAccess("/dev/stats_test", read_buffer.data(), read_buffer.size(), false);
        } else {
            std::vector<uint8_t> write_data = GenerateTestData(64, static_cast<uint8_t>(i));
            GetFilesystem()->HandleDeviceAccess("/dev/stats_test", write_data.data(), write_data.size(), true);
        }
    }

    // Verify statistics
    EXPECT_EQ(read_count.load(), 25);  // Half of operations were reads
    EXPECT_EQ(write_count.load(), 25); // Half of operations were writes
    EXPECT_EQ(bytes_read.load(), 25 * 128);  // 25 reads of 128 bytes each
    EXPECT_EQ(bytes_written.load(), 25 * 64); // 25 writes of 64 bytes each
}

// Device file error conditions and edge cases
TEST_F(PS4FilesystemDeviceTest, DeviceFileErrorConditionsAndEdgeCases) {
    EXPECT_TRUE(GetFilesystem()->InitializeAllDeviceFiles());

    // Test null pointer handling
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/dev/null", nullptr, 100, true));
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/dev/zero", nullptr, 100, false));

    // Test zero-size operations
    std::vector<uint8_t> buffer(10);
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/null", buffer.data(), 0, true));
    EXPECT_TRUE(GetFilesystem()->HandleDeviceAccess("/dev/zero", buffer.data(), 0, false));

    // Test very large operations
    const size_t large_size = 10 * 1024 * 1024; // 10MB
    std::vector<uint8_t> large_buffer(large_size);

    // /dev/null should handle large writes
    bool large_write_success = GetFilesystem()->HandleDeviceAccess("/dev/null", large_buffer.data(), large_size, true);
    EXPECT_TRUE(large_write_success);

    // /dev/zero should handle large reads
    bool large_read_success = GetFilesystem()->HandleDeviceAccess("/dev/zero", large_buffer.data(), large_size, false);
    EXPECT_TRUE(large_read_success);

    // Verify /dev/zero filled the entire large buffer with zeros
    if (large_read_success) {
        for (size_t i = 0; i < large_size; i += 1024) { // Sample every 1KB
            EXPECT_EQ(large_buffer[i], 0x00);
        }
    }

    // Test invalid device paths
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("", buffer.data(), buffer.size(), false));
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/dev/", buffer.data(), buffer.size(), false));
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/dev/invalid_device_name", buffer.data(), buffer.size(), false));

    // Test device path case sensitivity
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/DEV/NULL", buffer.data(), buffer.size(), true));
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/dev/NULL", buffer.data(), buffer.size(), true));
    EXPECT_FALSE(GetFilesystem()->HandleDeviceAccess("/dev/Null", buffer.data(), buffer.size(), true));
}

^D:\SSS\SRC\TESTS\CMAKEFILES\3.30.5-<PERSON>VC23\COMPILERIDCXX\DEBUG\CMAKECXXCOMPILERID.OBJ
/OUT:".\COMPILERIDCXX.EXE" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\VCPKG\INSTALLED\X64-WINDOWS\LIB" /LIBPATH:"D:\VCPKG\INSTALLED\X64-WINDOWS\LIB\MANUAL-LINK" KERNEL32.LIB USER32.LIB GDI32.LIB WINSPOOL.LIB COMDLG32.LIB ADVAPI32.LIB SHELL32.LIB OLE32.LIB OLEAUT32.LIB UUID.LIB ODBC32.LIB ODBCCP32.LIB "D:\VCPKG\INSTALLED\X64-WINDOWS\LIB\*.LIB" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\COMPILERIDCXX.PDB" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\COMPILERIDCXX.LIB" /MACHINE:X64 DEBUG\CMAKECXXCOMPILERID.OBJ


Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{0BB25EBA-86D7-3C70-AC92-21C472F04B48}"
	ProjectSection(ProjectDependencies) = postProject
		{D7858833-00F7-3750-8783-5EC90B7A715A} = {D7858833-00F7-3750-8783-5EC90B7A715A}
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B} = {4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{D571E34F-7FD8-3003-8086-B925E989D27B}"
	ProjectSection(ProjectDependencies) = postProject
		{D7858833-00F7-3750-8783-5EC90B7A715A} = {D7858833-00F7-3750-8783-5EC90B7A715A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{D7858833-00F7-3750-8783-5EC90B7A715A}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ps4_filesystem_tests", "ps4_filesystem_tests.vcxproj", "{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}"
	ProjectSection(ProjectDependencies) = postProject
		{D7858833-00F7-3750-8783-5EC90B7A715A} = {D7858833-00F7-3750-8783-5EC90B7A715A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "run_filesystem_tests", "run_filesystem_tests.vcxproj", "{7E13758E-F227-35FD-A731-C917C9AF903D}"
	ProjectSection(ProjectDependencies) = postProject
		{D7858833-00F7-3750-8783-5EC90B7A715A} = {D7858833-00F7-3750-8783-5EC90B7A715A}
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B} = {4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "run_filesystem_tests_verbose", "run_filesystem_tests_verbose.vcxproj", "{BD3C6A1B-9FD2-3BDC-9CF7-8ADC2CC33EB9}"
	ProjectSection(ProjectDependencies) = postProject
		{D7858833-00F7-3750-8783-5EC90B7A715A} = {D7858833-00F7-3750-8783-5EC90B7A715A}
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B} = {4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0BB25EBA-86D7-3C70-AC92-21C472F04B48}.Debug|x64.ActiveCfg = Debug|x64
		{0BB25EBA-86D7-3C70-AC92-21C472F04B48}.Release|x64.ActiveCfg = Release|x64
		{0BB25EBA-86D7-3C70-AC92-21C472F04B48}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0BB25EBA-86D7-3C70-AC92-21C472F04B48}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D571E34F-7FD8-3003-8086-B925E989D27B}.Debug|x64.ActiveCfg = Debug|x64
		{D571E34F-7FD8-3003-8086-B925E989D27B}.Release|x64.ActiveCfg = Release|x64
		{D571E34F-7FD8-3003-8086-B925E989D27B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D571E34F-7FD8-3003-8086-B925E989D27B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D7858833-00F7-3750-8783-5EC90B7A715A}.Debug|x64.ActiveCfg = Debug|x64
		{D7858833-00F7-3750-8783-5EC90B7A715A}.Debug|x64.Build.0 = Debug|x64
		{D7858833-00F7-3750-8783-5EC90B7A715A}.Release|x64.ActiveCfg = Release|x64
		{D7858833-00F7-3750-8783-5EC90B7A715A}.Release|x64.Build.0 = Release|x64
		{D7858833-00F7-3750-8783-5EC90B7A715A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D7858833-00F7-3750-8783-5EC90B7A715A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D7858833-00F7-3750-8783-5EC90B7A715A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D7858833-00F7-3750-8783-5EC90B7A715A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}.Debug|x64.ActiveCfg = Debug|x64
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}.Debug|x64.Build.0 = Debug|x64
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}.Release|x64.ActiveCfg = Release|x64
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}.Release|x64.Build.0 = Release|x64
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4E9F6FEF-F379-3FDC-A0C3-6C0D07E9871B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7E13758E-F227-35FD-A731-C917C9AF903D}.Debug|x64.ActiveCfg = Debug|x64
		{7E13758E-F227-35FD-A731-C917C9AF903D}.Release|x64.ActiveCfg = Release|x64
		{7E13758E-F227-35FD-A731-C917C9AF903D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7E13758E-F227-35FD-A731-C917C9AF903D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BD3C6A1B-9FD2-3BDC-9CF7-8ADC2CC33EB9}.Debug|x64.ActiveCfg = Debug|x64
		{BD3C6A1B-9FD2-3BDC-9CF7-8ADC2CC33EB9}.Release|x64.ActiveCfg = Release|x64
		{BD3C6A1B-9FD2-3BDC-9CF7-8ADC2CC33EB9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BD3C6A1B-9FD2-3BDC-9CF7-8ADC2CC33EB9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {31773E8E-314B-34EB-B175-AB19BFCF360E}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal

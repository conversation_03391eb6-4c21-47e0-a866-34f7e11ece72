add_test([=[FileLRUCacheTest.ConstructorSetsCorrectMaxSize]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.ConstructorSetsCorrectMaxSize]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.ConstructorSetsCorrectMaxSize]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.PutAndGetBasicOperation]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.PutAndGetBasicOperation]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.PutAndGetBasicOperation]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.GetNonExistentFileReturnsFalse]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.GetNonExistentFileReturnsFalse]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.GetNonExistentFileReturnsFalse]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.RemoveFileFromCache]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.RemoveFileFromCache]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.RemoveFileFromCache]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.ClearRemovesAllEntries]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.ClearRemovesAllEntries]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.ClearRemovesAllEntries]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.LRUEvictionWhenCacheFull]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.LRUEvictionWhenCacheFull]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.LRUEvictionWhenCacheFull]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.AccessUpdatesLRUOrder]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.AccessUpdatesLRUOrder]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.AccessUpdatesLRUOrder]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.SetMaxSizeTriggersEviction]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.SetMaxSizeTriggersEviction]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.SetMaxSizeTriggersEviction]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.InvalidateFileRemovesFromCache]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.InvalidateFileRemovesFromCache]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.InvalidateFileRemovesFromCache]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.InvalidatePatternRemovesMatchingFiles]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.InvalidatePatternRemovesMatchingFiles]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.InvalidatePatternRemovesMatchingFiles]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.HitRatioCalculation]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.HitRatioCalculation]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.HitRatioCalculation]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.EvictOldEntries]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.EvictOldEntries]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.EvictOldEntries]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.CompactCacheReducesSize]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.CompactCacheReducesSize]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.CompactCacheReducesSize]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.ConcurrentAccessIsSafe]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.ConcurrentAccessIsSafe]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.ConcurrentAccessIsSafe]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.EmptyFilenameHandling]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.EmptyFilenameHandling]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.EmptyFilenameHandling]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.LargeFileHandling]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.LargeFileHandling]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.LargeFileHandling]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[FileLRUCacheTest.ZeroSizeCacheHandling]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=FileLRUCacheTest.ZeroSizeCacheHandling]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[FileLRUCacheTest.ZeroSizeCacheHandling]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.MountValidDirectory]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.MountValidDirectory]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.MountValidDirectory]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.MountReadOnlyDirectory]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.MountReadOnlyDirectory]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.MountReadOnlyDirectory]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.MountPFSDirectory]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.MountPFSDirectory]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.MountPFSDirectory]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.MountNonExistentDirectory]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.MountNonExistentDirectory]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.MountNonExistentDirectory]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.MountEmptyPaths]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.MountEmptyPaths]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.MountEmptyPaths]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.MountDuplicateGuestPath]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.MountDuplicateGuestPath]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.MountDuplicateGuestPath]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.UnmountExistingMount]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.UnmountExistingMount]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.UnmountExistingMount]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.UnmountNonExistentMount]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.UnmountNonExistentMount]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.UnmountNonExistentMount]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.UnmountEmptyPath]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.UnmountEmptyPath]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.UnmountEmptyPath]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.UnmountAllClearsAllMounts]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.UnmountAllClearsAllMounts]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.UnmountAllClearsAllMounts]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.GetHostPathForValidMount]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.GetHostPathForValidMount]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.GetHostPathForValidMount]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.GetHostPathForInvalidMount]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.GetHostPathForInvalidMount]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.GetHostPathForInvalidMount]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.GetHostPathWithReadOnlyFlag]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.GetHostPathWithReadOnlyFlag]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.GetHostPathWithReadOnlyFlag]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.GetHostPathEmptyInput]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.GetHostPathEmptyInput]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.GetHostPathEmptyInput]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.MultipleMountPoints]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.MultipleMountPoints]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.MultipleMountPoints]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.OverlappingMountPaths]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.OverlappingMountPaths]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.OverlappingMountPaths]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.PathNormalization]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.PathNormalization]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.PathNormalization]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.CaseSensitivity]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.CaseSensitivity]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.CaseSensitivity]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.SpecialCharactersInPaths]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.SpecialCharactersInPaths]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.SpecialCharactersInPaths]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[MountPointManagerTest.ConcurrentAccess]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=MountPointManagerTest.ConcurrentAccess]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[MountPointManagerTest.ConcurrentAccess]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.CreateHandleReturnsValidFd]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.CreateHandleReturnsValidFd]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.CreateHandleReturnsValidFd]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.CreateMultipleHandles]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.CreateMultipleHandles]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.CreateMultipleHandles]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.DeleteHandleRemovesFromTable]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.DeleteHandleRemovesFromTable]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.DeleteHandleRemovesFromTable]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.DeleteNonExistentHandle]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.DeleteNonExistentHandle]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.DeleteNonExistentHandle]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.StandardHandlesExist]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.StandardHandlesExist]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.StandardHandlesExist]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.StandardHandlesHaveCorrectProperties]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.StandardHandlesHaveCorrectProperties]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.StandardHandlesHaveCorrectProperties]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.SetFileProperties]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.SetFileProperties]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.SetFileProperties]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.FileTypeHandling]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.FileTypeHandling]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.FileTypeHandling]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.HandleReuse]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.HandleReuse]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.HandleReuse]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.HandleSequentialAllocation]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.HandleSequentialAllocation]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.HandleSequentialAllocation]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.GetFileWithInvalidFd]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.GetFileWithInvalidFd]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.GetFileWithInvalidFd]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.GetFileAfterDeletion]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.GetFileAfterDeletion]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.GetFileAfterDeletion]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.ConcurrentHandleCreation]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.ConcurrentHandleCreation]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.ConcurrentHandleCreation]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.ConcurrentHandleDeletion]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.ConcurrentHandleDeletion]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.ConcurrentHandleDeletion]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.ConcurrentCreateAndDelete]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.ConcurrentCreateAndDelete]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.ConcurrentCreateAndDelete]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[HandleTableTest.ManyHandles]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=HandleTableTest.ManyHandles]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[HandleTableTest.ManyHandles]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.InitializePFS]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.InitializePFS]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.InitializePFS]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.CreatePFSFile]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.CreatePFSFile]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.CreatePFSFile]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.MountUnmountPFS]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.MountUnmountPFS]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.MountUnmountPFS]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.ReadWritePFSFile]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.ReadWritePFSFile]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.ReadWritePFSFile]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.ValidatePFSChecksum]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.ValidatePFSChecksum]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.ValidatePFSChecksum]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.DeletePFSFile]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.DeletePFSFile]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.DeletePFSFile]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.PFSErrorHandling]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.PFSErrorHandling]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.PFSErrorHandling]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.EncryptionDecryptionFunctionality]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.EncryptionDecryptionFunctionality]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.EncryptionDecryptionFunctionality]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.BlockLevelOperations]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.BlockLevelOperations]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.BlockLevelOperations]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.LargeFileHandling]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.LargeFileHandling]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.LargeFileHandling]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.ConcurrentPFSAccess]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.ConcurrentPFSAccess]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.ConcurrentPFSAccess]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.PFSCorruptionRecovery]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.PFSCorruptionRecovery]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.PFSCorruptionRecovery]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.PFSKeyManagement]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.PFSKeyManagement]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.PFSKeyManagement]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemPFSTest.PFSFileSizeAndBoundaries]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemPFSTest.PFSFileSizeAndBoundaries]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemPFSTest.PFSFileSizeAndBoundaries]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.InitializeAllDeviceFiles]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.InitializeAllDeviceFiles]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.InitializeAllDeviceFiles]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.DetermineFileType]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.DetermineFileType]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.DetermineFileType]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.RegisterDeviceHandler]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.RegisterDeviceHandler]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.RegisterDeviceHandler]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.HandleDeviceAccess]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.HandleDeviceAccess]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.HandleDeviceAccess]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.ValidatePS4Permissions]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.ValidatePS4Permissions]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.ValidatePS4Permissions]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.DeviceErrorHandling]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.DeviceErrorHandling]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.DeviceErrorHandling]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.MultipleDeviceHandlers]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.MultipleDeviceHandlers]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.MultipleDeviceHandlers]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.ReplaceDeviceHandler]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.ReplaceDeviceHandler]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.ReplaceDeviceHandler]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.StandardPS4DeviceFiles]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.StandardPS4DeviceFiles]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.StandardPS4DeviceFiles]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.DeviceFileIntegrationWithFileOperations]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.DeviceFileIntegrationWithFileOperations]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.DeviceFileIntegrationWithFileOperations]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.ConcurrentDeviceAccess]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.ConcurrentDeviceAccess]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.ConcurrentDeviceAccess]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.DeviceFilePermissionsAndSecurity]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.DeviceFilePermissionsAndSecurity]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.DeviceFilePermissionsAndSecurity]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.SpecialDeviceBehaviors]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.SpecialDeviceBehaviors]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.SpecialDeviceBehaviors]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.DeviceFileStatisticsAndMonitoring]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.DeviceFileStatisticsAndMonitoring]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.DeviceFileStatisticsAndMonitoring]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
add_test([=[PS4FilesystemDeviceTest.DeviceFileErrorConditionsAndEdgeCases]=]  D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe [==[--gtest_filter=PS4FilesystemDeviceTest.DeviceFileErrorConditionsAndEdgeCases]==] --gtest_also_run_disabled_tests)
set_tests_properties([=[PS4FilesystemDeviceTest.DeviceFileErrorConditionsAndEdgeCases]=]  PROPERTIES WORKING_DIRECTORY D:/sss/src/tests SKIP_REGULAR_EXPRESSION [==[\[  SKIPPED \]]==] VS_DEBUGGER_WORKING_DIRECTORY D:/sss/src/tests)
set(  ps4_filesystem_tests_TESTS FileLRUCacheTest.ConstructorSetsCorrectMaxSize FileLRUCacheTest.PutAndGetBasicOperation FileLRUCacheTest.GetNonExistentFileReturnsFalse FileLRUCacheTest.RemoveFileFromCache FileLRUCacheTest.ClearRemovesAllEntries FileLRUCacheTest.LRUEvictionWhenCacheFull FileLRUCacheTest.AccessUpdatesLRUOrder FileLRUCacheTest.SetMaxSizeTriggersEviction FileLRUCacheTest.InvalidateFileRemovesFromCache FileLRUCacheTest.InvalidatePatternRemovesMatchingFiles FileLRUCacheTest.HitRatioCalculation FileLRUCacheTest.EvictOldEntries FileLRUCacheTest.CompactCacheReducesSize FileLRUCacheTest.ConcurrentAccessIsSafe FileLRUCacheTest.EmptyFilenameHandling FileLRUCacheTest.LargeFileHandling FileLRUCacheTest.ZeroSizeCacheHandling MountPointManagerTest.MountValidDirectory MountPointManagerTest.MountReadOnlyDirectory MountPointManagerTest.MountPFSDirectory MountPointManagerTest.MountNonExistentDirectory MountPointManagerTest.MountEmptyPaths MountPointManagerTest.MountDuplicateGuestPath MountPointManagerTest.UnmountExistingMount MountPointManagerTest.UnmountNonExistentMount MountPointManagerTest.UnmountEmptyPath MountPointManagerTest.UnmountAllClearsAllMounts MountPointManagerTest.GetHostPathForValidMount MountPointManagerTest.GetHostPathForInvalidMount MountPointManagerTest.GetHostPathWithReadOnlyFlag MountPointManagerTest.GetHostPathEmptyInput MountPointManagerTest.MultipleMountPoints MountPointManagerTest.OverlappingMountPaths MountPointManagerTest.PathNormalization MountPointManagerTest.CaseSensitivity MountPointManagerTest.SpecialCharactersInPaths MountPointManagerTest.ConcurrentAccess HandleTableTest.CreateHandleReturnsValidFd HandleTableTest.CreateMultipleHandles HandleTableTest.DeleteHandleRemovesFromTable HandleTableTest.DeleteNonExistentHandle HandleTableTest.StandardHandlesExist HandleTableTest.StandardHandlesHaveCorrectProperties HandleTableTest.SetFileProperties HandleTableTest.FileTypeHandling HandleTableTest.HandleReuse HandleTableTest.HandleSequentialAllocation HandleTableTest.GetFileWithInvalidFd HandleTableTest.GetFileAfterDeletion HandleTableTest.ConcurrentHandleCreation HandleTableTest.ConcurrentHandleDeletion HandleTableTest.ConcurrentCreateAndDelete HandleTableTest.ManyHandles PS4FilesystemPFSTest.InitializePFS PS4FilesystemPFSTest.CreatePFSFile PS4FilesystemPFSTest.MountUnmountPFS PS4FilesystemPFSTest.ReadWritePFSFile PS4FilesystemPFSTest.ValidatePFSChecksum PS4FilesystemPFSTest.DeletePFSFile PS4FilesystemPFSTest.PFSErrorHandling PS4FilesystemPFSTest.EncryptionDecryptionFunctionality PS4FilesystemPFSTest.BlockLevelOperations PS4FilesystemPFSTest.LargeFileHandling PS4FilesystemPFSTest.ConcurrentPFSAccess PS4FilesystemPFSTest.PFSCorruptionRecovery PS4FilesystemPFSTest.PFSKeyManagement PS4FilesystemPFSTest.PFSFileSizeAndBoundaries PS4FilesystemDeviceTest.InitializeAllDeviceFiles PS4FilesystemDeviceTest.DetermineFileType PS4FilesystemDeviceTest.RegisterDeviceHandler PS4FilesystemDeviceTest.HandleDeviceAccess PS4FilesystemDeviceTest.ValidatePS4Permissions PS4FilesystemDeviceTest.DeviceErrorHandling PS4FilesystemDeviceTest.MultipleDeviceHandlers PS4FilesystemDeviceTest.ReplaceDeviceHandler PS4FilesystemDeviceTest.StandardPS4DeviceFiles PS4FilesystemDeviceTest.DeviceFileIntegrationWithFileOperations PS4FilesystemDeviceTest.ConcurrentDeviceAccess PS4FilesystemDeviceTest.DeviceFilePermissionsAndSecurity PS4FilesystemDeviceTest.SpecialDeviceBehaviors PS4FilesystemDeviceTest.DeviceFileStatisticsAndMonitoring PS4FilesystemDeviceTest.DeviceFileErrorConditionsAndEdgeCases)

@echo off
REM PS4 Filesystem Test Runner Script for Windows

echo ========================================
echo PS4 Filesystem Test Suite
echo ========================================

REM Check if build directory exists
if not exist "..\build" (
    echo Error: Build directory not found. Please build the project first.
    echo Run: cmake --build ../build --config Debug
    pause
    exit /b 1
)

REM Change to build directory
cd ..\build

REM Check if test executable exists
if not exist "tests\ps4_filesystem_tests.exe" (
    echo Error: Test executable not found. Please build the tests first.
    echo Run: cmake --build . --target ps4_filesystem_tests --config Debug
    pause
    exit /b 1
)

echo Running PS4 Filesystem Tests...
echo.

REM Run all tests
tests\ps4_filesystem_tests.exe --gtest_output=xml:test_results.xml

set TEST_RESULT=%ERRORLEVEL%

echo.
echo ========================================

if %TEST_RESULT% equ 0 (
    echo All tests PASSED!
    echo Test results saved to: test_results.xml
) else (
    echo Some tests FAILED!
    echo Check the output above for details.
    echo Test results saved to: test_results.xml
)

echo ========================================

REM Return to tests directory
cd tests

pause
exit /b %TEST_RESULT%

<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\sss\src\tests\test_file_lru_cache.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\tests\test_mount_point_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\tests\test_handle_table.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\tests\test_ps4_filesystem_pfs.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\tests\test_ps4_filesystem_device.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\tests\utils\test_fixtures.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\tests\utils\test_helpers.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\tests\utils\filesystem_test_base.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\tests\main_test.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\ps4\ps4_filesystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\sss\src\common\lock_ordering.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\sss\src\tests\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_json.natvis" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{88A9C776-6FF9-3CD6-9A3F-90606D8AD123}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

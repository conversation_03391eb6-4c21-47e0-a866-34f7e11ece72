# CMake generated Testfile for 
# Source directory: D:/sss/src/tests
# Build directory: D:/sss/src/tests/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
include("D:/sss/src/tests/build/ps4_filesystem_tests[1]_include.cmake")
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(FileLRUCacheTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"FileLRUCacheTest.*\"")
  set_tests_properties(FileLRUCacheTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;106;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(FileLRUCacheTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"FileLRUCacheTest.*\"")
  set_tests_properties(FileLRUCacheTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;106;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(FileLRUCacheTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"FileLRUCacheTest.*\"")
  set_tests_properties(FileLRUCacheTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;106;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(FileLRUCacheTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"FileLRUCacheTest.*\"")
  set_tests_properties(FileLRUCacheTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;106;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(FileLRUCacheTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(MountPointManagerTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"MountPointManagerTest.*\"")
  set_tests_properties(MountPointManagerTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;107;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(MountPointManagerTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"MountPointManagerTest.*\"")
  set_tests_properties(MountPointManagerTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;107;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(MountPointManagerTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"MountPointManagerTest.*\"")
  set_tests_properties(MountPointManagerTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;107;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(MountPointManagerTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"MountPointManagerTest.*\"")
  set_tests_properties(MountPointManagerTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;107;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(MountPointManagerTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(HandleTableTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"HandleTableTest.*\"")
  set_tests_properties(HandleTableTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;108;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(HandleTableTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"HandleTableTest.*\"")
  set_tests_properties(HandleTableTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;108;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(HandleTableTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"HandleTableTest.*\"")
  set_tests_properties(HandleTableTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;108;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(HandleTableTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"HandleTableTest.*\"")
  set_tests_properties(HandleTableTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;108;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(HandleTableTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(PS4FilesystemCoreTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemCoreTest.*\"")
  set_tests_properties(PS4FilesystemCoreTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;109;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(PS4FilesystemCoreTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemCoreTest.*\"")
  set_tests_properties(PS4FilesystemCoreTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;109;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(PS4FilesystemCoreTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemCoreTest.*\"")
  set_tests_properties(PS4FilesystemCoreTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;109;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(PS4FilesystemCoreTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemCoreTest.*\"")
  set_tests_properties(PS4FilesystemCoreTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;109;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(PS4FilesystemCoreTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(PS4FilesystemPFSTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemPFSTest.*\"")
  set_tests_properties(PS4FilesystemPFSTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;110;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(PS4FilesystemPFSTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemPFSTest.*\"")
  set_tests_properties(PS4FilesystemPFSTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;110;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(PS4FilesystemPFSTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemPFSTest.*\"")
  set_tests_properties(PS4FilesystemPFSTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;110;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(PS4FilesystemPFSTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemPFSTest.*\"")
  set_tests_properties(PS4FilesystemPFSTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;110;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(PS4FilesystemPFSTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(PS4FilesystemDeviceTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemDeviceTest.*\"")
  set_tests_properties(PS4FilesystemDeviceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;111;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(PS4FilesystemDeviceTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemDeviceTest.*\"")
  set_tests_properties(PS4FilesystemDeviceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;111;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(PS4FilesystemDeviceTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemDeviceTest.*\"")
  set_tests_properties(PS4FilesystemDeviceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;111;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(PS4FilesystemDeviceTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemDeviceTest.*\"")
  set_tests_properties(PS4FilesystemDeviceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;111;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(PS4FilesystemDeviceTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(PS4FilesystemIntegrationTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemIntegrationTest.*\"")
  set_tests_properties(PS4FilesystemIntegrationTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;112;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(PS4FilesystemIntegrationTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemIntegrationTest.*\"")
  set_tests_properties(PS4FilesystemIntegrationTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;112;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(PS4FilesystemIntegrationTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemIntegrationTest.*\"")
  set_tests_properties(PS4FilesystemIntegrationTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;112;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(PS4FilesystemIntegrationTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemIntegrationTest.*\"")
  set_tests_properties(PS4FilesystemIntegrationTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;112;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(PS4FilesystemIntegrationTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(PS4FilesystemPerformanceTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemPerformanceTest.*\"")
  set_tests_properties(PS4FilesystemPerformanceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;113;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(PS4FilesystemPerformanceTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemPerformanceTest.*\"")
  set_tests_properties(PS4FilesystemPerformanceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;113;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(PS4FilesystemPerformanceTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemPerformanceTest.*\"")
  set_tests_properties(PS4FilesystemPerformanceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;113;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(PS4FilesystemPerformanceTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"PS4FilesystemPerformanceTest.*\"")
  set_tests_properties(PS4FilesystemPerformanceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;113;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(PS4FilesystemPerformanceTests NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(LockOrderingComplianceTests "D:/sss/src/tests/build/Debug/ps4_filesystem_tests.exe" "--gtest_filter=\"LockOrderingComplianceTest.*\"")
  set_tests_properties(LockOrderingComplianceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;114;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(LockOrderingComplianceTests "D:/sss/src/tests/build/Release/ps4_filesystem_tests.exe" "--gtest_filter=\"LockOrderingComplianceTest.*\"")
  set_tests_properties(LockOrderingComplianceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;114;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(LockOrderingComplianceTests "D:/sss/src/tests/build/MinSizeRel/ps4_filesystem_tests.exe" "--gtest_filter=\"LockOrderingComplianceTest.*\"")
  set_tests_properties(LockOrderingComplianceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;114;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(LockOrderingComplianceTests "D:/sss/src/tests/build/RelWithDebInfo/ps4_filesystem_tests.exe" "--gtest_filter=\"LockOrderingComplianceTest.*\"")
  set_tests_properties(LockOrderingComplianceTests PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;114;add_test;D:/sss/src/tests/CMakeLists.txt;0;")
else()
  add_test(LockOrderingComplianceTests NOT_AVAILABLE)
endif()
add_test(AllTestsInMain "main")
set_tests_properties(AllTestsInMain PROPERTIES  _BACKTRACE_TRIPLES "D:/sss/src/tests/CMakeLists.txt;115;add_test;D:/sss/src/tests/CMakeLists.txt;0;")

^D:\SSS\SRC\TESTS\TEST_FILE_LRU_CACHE.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\TEST_FILE_LRU_CACHE.CPP
^D:\SSS\SRC\TESTS\TEST_MOUNT_POINT_MANAGER.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\TEST_MOUNT_POINT_MANAGER.CPP
^D:\SSS\SRC\TESTS\TEST_HANDLE_TABLE.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\TEST_HANDLE_TABLE.CPP
^D:\SSS\SRC\TESTS\TEST_PS4_FILESYSTEM_PFS.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\TEST_PS4_FILESYSTEM_PFS.CPP
^D:\SSS\SRC\TESTS\TEST_PS4_FILESYSTEM_DEVICE.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\TEST_PS4_FILESYSTEM_DEVICE.CPP
^D:\SSS\SRC\TESTS\UTILS\TEST_FIXTURES.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\UTILS\TEST_FIXTURES.CPP
^D:\SSS\SRC\TESTS\UTILS\TEST_HELPERS.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\UTILS\TEST_HELPERS.CPP
^D:\SSS\SRC\TESTS\UTILS\FILESYSTEM_TEST_BASE.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\UTILS\FILESYSTEM_TEST_BASE.CPP
^D:\SSS\SRC\TESTS\MAIN_TEST.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\TESTS\MAIN_TEST.CPP
^D:\SSS\SRC\PS4\PS4_FILESYSTEM.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\PS4\PS4_FILESYSTEM.CPP
^D:\SSS\SRC\COMMON\LOCK_ORDERING.CPP
/c /ID:\SSS\SRC\TESTS /ID:\SSS\SRC\TESTS\.. /ID:\SSS\SRC\TESTS\..\PS4 /ID:\SSS\SRC\TESTS\..\COMMON /ID:\SSS\SRC\TESTS\..\MEMORY /ID:\SSS\SRC\TESTS\..\EMULATOR /ID:\SSS\SRC\TESTS\..\CPU /Zi /nologo /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D DEBUG_MODE /D TESTING_MODE /D _DEBUG /D SPDLOG_NO_COMPILE_TIME_FMT /D SPDLOG_SHARED_LIB /D SPDLOG_COMPILED_LIB /D SPDLOG_FMT_EXTERNAL /D FMT_SHARED /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"PS4_FILESYSTEM_TESTS.DIR\DEBUG\\" /Fd"PS4_FILESYSTEM_TESTS.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP /wd4251 /wd4275  /external:I "D:/grok/include" /external:I "D:/vcpkg/installed/x64-windows/include" /utf-8 D:\SSS\SRC\COMMON\LOCK_ORDERING.CPP

/**
 * @file test_ps4_filesystem_pfs.cpp
 * @brief Tests for PS4 filesystem PFS (PlayStation File System) functionality
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "ps4/ps4_filesystem.h"
#include "utils/test_fixtures.h"

using namespace ps4;
using namespace ps4::test;

class PS4FilesystemPFSTest : public PFSTestFixture {
protected:
    void SetUp() override {
        PFSTestFixture::SetUp();
    }
};

// PFS initialization tests
TEST_F(PS4FilesystemPFSTest, InitializePFS) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());
}

// PFS file creation tests
TEST_F(PS4FilesystemPFSTest, CreatePFSFile) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());
    
    // Create encrypted PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/test_encrypted.bin", 4096, true));
    
    // Create unencrypted PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/test_plain.bin", 4096, false));
}

// PFS mount/unmount tests
TEST_F(PS4FilesystemPFSTest, MountUnmountPFS) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());
    
    // Create PFS file first
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/mount_test.pfs", 8192, true));
    
    // Generate key for mounting
    std::vector<uint8_t> key(32, 0xAB); // 256-bit key
    
    // Mount PFS
    EXPECT_TRUE(GetFilesystem()->MountPFS("/pfs/mount_test.pfs", "/mounted_pfs", key));
    
    // Unmount PFS
    EXPECT_TRUE(GetFilesystem()->UnmountPFS("/mounted_pfs"));
}

// PFS read/write tests
TEST_F(PS4FilesystemPFSTest, ReadWritePFSFile) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());
    
    // Create PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/rw_test.bin", 4096, false));
    
    // Write data to PFS file
    std::vector<uint8_t> write_data = GenerateTestData(1024, 0xCD);
    ssize_t bytes_written = GetFilesystem()->WritePFSFile("/pfs/rw_test.bin", 
                                                         write_data.data(), 0, write_data.size());
    EXPECT_EQ(bytes_written, static_cast<ssize_t>(write_data.size()));
    
    // Read data from PFS file
    std::vector<uint8_t> read_buffer(write_data.size());
    ssize_t bytes_read = GetFilesystem()->ReadPFSFile("/pfs/rw_test.bin", 
                                                     read_buffer.data(), 0, read_buffer.size());
    EXPECT_EQ(bytes_read, static_cast<ssize_t>(write_data.size()));
    EXPECT_EQ(read_buffer, write_data);
}

// PFS checksum validation tests
TEST_F(PS4FilesystemPFSTest, ValidatePFSChecksum) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());
    
    // Create PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/checksum_test.bin", 4096, false));
    
    // Write some data
    std::vector<uint8_t> test_data = GenerateTestData(512, 0xEF);
    GetFilesystem()->WritePFSFile("/pfs/checksum_test.bin", test_data.data(), 0, test_data.size());
    
    // Validate checksum
    EXPECT_TRUE(GetFilesystem()->ValidatePFSChecksum("/pfs/checksum_test.bin"));
}

// PFS deletion tests
TEST_F(PS4FilesystemPFSTest, DeletePFSFile) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());
    
    // Create PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/delete_test.bin", 4096, false));
    
    // Delete PFS file
    EXPECT_TRUE(GetFilesystem()->DeletePFSFile("/pfs/delete_test.bin"));
    
    // Verify file is deleted (reading should fail)
    std::vector<uint8_t> buffer(100);
    ssize_t bytes_read = GetFilesystem()->ReadPFSFile("/pfs/delete_test.bin", buffer.data(), 0, buffer.size());
    EXPECT_EQ(bytes_read, -1);
}

// PFS error handling tests
TEST_F(PS4FilesystemPFSTest, PFSErrorHandling) {
    // Test operations without initializing PFS
    EXPECT_FALSE(GetFilesystem()->CreatePFSFile("/pfs/error_test.bin", 4096, false));
    
    std::vector<uint8_t> key(32, 0xAB);
    EXPECT_FALSE(GetFilesystem()->MountPFS("/nonexistent.pfs", "/mount", key));
    
    std::vector<uint8_t> buffer(100);
    EXPECT_EQ(GetFilesystem()->ReadPFSFile("/nonexistent.bin", buffer.data(), 0, buffer.size()), -1);
}

// Encryption/decryption functionality tests
TEST_F(PS4FilesystemPFSTest, EncryptionDecryptionFunctionality) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());

    // Create encrypted PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/encrypted_test.bin", 8192, true));

    // Generate test data
    std::vector<uint8_t> original_data = GenerateTestData(2048, 0xAB);

    // Write encrypted data
    ssize_t bytes_written = GetFilesystem()->WritePFSFile("/pfs/encrypted_test.bin",
                                                         original_data.data(), 0, original_data.size());
    EXPECT_EQ(bytes_written, static_cast<ssize_t>(original_data.size()));

    // Read encrypted data back
    std::vector<uint8_t> read_buffer(original_data.size());
    ssize_t bytes_read = GetFilesystem()->ReadPFSFile("/pfs/encrypted_test.bin",
                                                     read_buffer.data(), 0, read_buffer.size());
    EXPECT_EQ(bytes_read, static_cast<ssize_t>(original_data.size()));
    EXPECT_EQ(read_buffer, original_data);

    // Validate checksum for encrypted file
    EXPECT_TRUE(GetFilesystem()->ValidatePFSChecksum("/pfs/encrypted_test.bin"));
}

// Block-level operations tests
TEST_F(PS4FilesystemPFSTest, BlockLevelOperations) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());

    // Create PFS file with specific block size
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/block_test.bin", 16384, false)); // 16KB file

    // Test writing across block boundaries
    const size_t block_size = 4096; // Typical PFS block size
    std::vector<uint8_t> cross_block_data = GenerateTestData(block_size + 512, 0xCD);

    // Write data that spans multiple blocks
    ssize_t bytes_written = GetFilesystem()->WritePFSFile("/pfs/block_test.bin",
                                                         cross_block_data.data(),
                                                         block_size - 256, // Start near end of first block
                                                         cross_block_data.size());
    EXPECT_EQ(bytes_written, static_cast<ssize_t>(cross_block_data.size()));

    // Read back the data
    std::vector<uint8_t> read_buffer(cross_block_data.size());
    ssize_t bytes_read = GetFilesystem()->ReadPFSFile("/pfs/block_test.bin",
                                                     read_buffer.data(),
                                                     block_size - 256,
                                                     read_buffer.size());
    EXPECT_EQ(bytes_read, static_cast<ssize_t>(cross_block_data.size()));
    EXPECT_EQ(read_buffer, cross_block_data);
}

// Large file handling tests
TEST_F(PS4FilesystemPFSTest, LargeFileHandling) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());

    // Create large PFS file (1MB)
    const size_t large_file_size = 1024 * 1024;
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/large_test.bin", large_file_size, false));

    // Generate large test data
    std::vector<uint8_t> large_data = GenerateTestData(large_file_size / 2, 0xEF);

    // Write large data in chunks
    const size_t chunk_size = 64 * 1024; // 64KB chunks
    for (size_t offset = 0; offset < large_data.size(); offset += chunk_size) {
        size_t current_chunk_size = std::min(chunk_size, large_data.size() - offset);

        ssize_t bytes_written = GetFilesystem()->WritePFSFile("/pfs/large_test.bin",
                                                             large_data.data() + offset,
                                                             offset,
                                                             current_chunk_size);
        EXPECT_EQ(bytes_written, static_cast<ssize_t>(current_chunk_size));
    }

    // Read back large data in chunks and verify
    std::vector<uint8_t> read_buffer(large_data.size());
    for (size_t offset = 0; offset < large_data.size(); offset += chunk_size) {
        size_t current_chunk_size = std::min(chunk_size, large_data.size() - offset);

        ssize_t bytes_read = GetFilesystem()->ReadPFSFile("/pfs/large_test.bin",
                                                         read_buffer.data() + offset,
                                                         offset,
                                                         current_chunk_size);
        EXPECT_EQ(bytes_read, static_cast<ssize_t>(current_chunk_size));
    }

    EXPECT_EQ(read_buffer, large_data);

    // Validate checksum for large file
    EXPECT_TRUE(GetFilesystem()->ValidatePFSChecksum("/pfs/large_test.bin"));
}

// Concurrent PFS access tests
TEST_F(PS4FilesystemPFSTest, ConcurrentPFSAccess) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());

    // Create multiple PFS files for concurrent testing
    const int num_files = 4;
    std::vector<std::string> pfs_files;

    for (int i = 0; i < num_files; ++i) {
        std::string filename = "/pfs/concurrent_" + std::to_string(i) + ".bin";
        EXPECT_TRUE(GetFilesystem()->CreatePFSFile(filename, 4096, false));
        pfs_files.push_back(filename);
    }

    const int num_threads = 4;
    const int operations_per_thread = 50;
    std::vector<std::thread> threads;
    std::atomic<int> successful_operations{0};
    std::atomic<bool> error_occurred{false};

    // Launch threads performing concurrent PFS operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, operations_per_thread, &pfs_files, &successful_operations, &error_occurred]() {
            try {
                for (int i = 0; i < operations_per_thread; ++i) {
                    std::string filename = pfs_files[t % pfs_files.size()];

                    // Generate unique test data for this thread/operation
                    std::vector<uint8_t> test_data = GenerateTestData(256, static_cast<uint8_t>(t * 16 + i));

                    // Write data
                    ssize_t bytes_written = GetFilesystem()->WritePFSFile(filename,
                                                                         test_data.data(),
                                                                         i * 256,
                                                                         test_data.size());
                    if (bytes_written == static_cast<ssize_t>(test_data.size())) {
                        // Read data back
                        std::vector<uint8_t> read_buffer(test_data.size());
                        ssize_t bytes_read = GetFilesystem()->ReadPFSFile(filename,
                                                                         read_buffer.data(),
                                                                         i * 256,
                                                                         read_buffer.size());

                        if (bytes_read == static_cast<ssize_t>(test_data.size()) && read_buffer == test_data) {
                            successful_operations++;
                        }
                    }

                    // Small delay to increase contention
                    if (i % 10 == 0) {
                        std::this_thread::sleep_for(std::chrono::microseconds(100));
                    }
                }
            } catch (const std::exception& e) {
                (void)e; // Mark as used to avoid compiler warning
                error_occurred = true;
            }
        });
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    EXPECT_FALSE(error_occurred.load()) << "Error occurred during concurrent PFS access";
    EXPECT_GT(successful_operations.load(), 0) << "No successful PFS operations completed";
}

// PFS corruption recovery tests
TEST_F(PS4FilesystemPFSTest, PFSCorruptionRecovery) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());

    // Create PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/corruption_test.bin", 4096, false));

    // Write valid data
    std::vector<uint8_t> valid_data = GenerateTestData(1024, 0xAB);
    ssize_t bytes_written = GetFilesystem()->WritePFSFile("/pfs/corruption_test.bin",
                                                         valid_data.data(), 0, valid_data.size());
    EXPECT_EQ(bytes_written, static_cast<ssize_t>(valid_data.size()));

    // Verify checksum is valid
    EXPECT_TRUE(GetFilesystem()->ValidatePFSChecksum("/pfs/corruption_test.bin"));

    // Simulate corruption by writing invalid data at a different offset
    // This should be detected by checksum validation
    std::vector<uint8_t> corrupt_data = GenerateTestData(512, 0xFF);
    GetFilesystem()->WritePFSFile("/pfs/corruption_test.bin",
                                 corrupt_data.data(), 2048, corrupt_data.size());

    // Try to read the original data - should still work for the valid portion
    std::vector<uint8_t> read_buffer(valid_data.size());
    ssize_t bytes_read = GetFilesystem()->ReadPFSFile("/pfs/corruption_test.bin",
                                                     read_buffer.data(), 0, read_buffer.size());
    EXPECT_EQ(bytes_read, static_cast<ssize_t>(valid_data.size()));
    EXPECT_EQ(read_buffer, valid_data);
}

// PFS key management tests
TEST_F(PS4FilesystemPFSTest, PFSKeyManagement) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());

    // Create encrypted PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/key_test.pfs", 8192, true));

    // Generate multiple keys for testing
    std::vector<uint8_t> key1(32, 0xAA);
    std::vector<uint8_t> key2(32, 0xBB);
    std::vector<uint8_t> key3(32, 0xCC);

    // Mount with first key
    EXPECT_TRUE(GetFilesystem()->MountPFS("/pfs/key_test.pfs", "/mount1", key1));

    // Mount with second key to different mount point
    EXPECT_TRUE(GetFilesystem()->MountPFS("/pfs/key_test.pfs", "/mount2", key2));

    // Unmount both
    EXPECT_TRUE(GetFilesystem()->UnmountPFS("/mount1"));
    EXPECT_TRUE(GetFilesystem()->UnmountPFS("/mount2"));

    // Test mounting with invalid key (should still succeed but data may be corrupted)
    EXPECT_TRUE(GetFilesystem()->MountPFS("/pfs/key_test.pfs", "/mount3", key3));
    EXPECT_TRUE(GetFilesystem()->UnmountPFS("/mount3"));
}

// PFS file size and boundary tests
TEST_F(PS4FilesystemPFSTest, PFSFileSizeAndBoundaries) {
    EXPECT_TRUE(GetFilesystem()->InitializePFS());

    // Test minimum size PFS file
    EXPECT_TRUE(GetFilesystem()->CreatePFSFile("/pfs/min_size.bin", 1024, false));

    // Test writing at exact boundaries
    std::vector<uint8_t> boundary_data = GenerateTestData(1024, 0xBD);
    ssize_t bytes_written = GetFilesystem()->WritePFSFile("/pfs/min_size.bin",
                                                         boundary_data.data(), 0, boundary_data.size());
    EXPECT_EQ(bytes_written, static_cast<ssize_t>(boundary_data.size()));

    // Test reading at exact boundaries
    std::vector<uint8_t> read_buffer(boundary_data.size());
    ssize_t bytes_read = GetFilesystem()->ReadPFSFile("/pfs/min_size.bin",
                                                     read_buffer.data(), 0, read_buffer.size());
    EXPECT_EQ(bytes_read, static_cast<ssize_t>(boundary_data.size()));
    EXPECT_EQ(read_buffer, boundary_data);

    // Test writing beyond file size (should fail or truncate)
    std::vector<uint8_t> overflow_data = GenerateTestData(512, 0x0F);
    ssize_t overflow_written = GetFilesystem()->WritePFSFile("/pfs/min_size.bin",
                                                            overflow_data.data(), 1024, overflow_data.size());
    // This should either fail or write only what fits
    EXPECT_LE(overflow_written, 0);

    // Test reading beyond file size (should return partial or fail)
    std::vector<uint8_t> overflow_buffer(512);
    ssize_t overflow_read = GetFilesystem()->ReadPFSFile("/pfs/min_size.bin",
                                                        overflow_buffer.data(), 1024, overflow_buffer.size());
    EXPECT_LE(overflow_read, 0);
}
